/**
 * Retargeting and Conversion Optimization Engine for ResearchSat
 * Implements smart popups, exit-intent, and conversion optimization
 */

import cookieManager, { COOKIE_CATEGORIES } from './cookieManager.js';
import analyticsTracker from './analyticsTracker.js';
import leadScoringEngine from './leadScoring.js';
import personalizationEngine from './personalizationEngine.js';

// Trigger conditions for different retargeting campaigns
export const TRIGGER_CONDITIONS = {
  EXIT_INTENT: {
    name: 'exit_intent',
    description: 'User moves cursor to close tab/window',
    cooldown: 24 * 60 * 60 * 1000, // 24 hours
    maxShows: 2
  },
  TIME_ON_SITE: {
    name: 'time_on_site',
    description: 'User spends significant time on site',
    threshold: 120000, // 2 minutes
    cooldown: 12 * 60 * 60 * 1000, // 12 hours
    maxShows: 3
  },
  SCROLL_DEPTH: {
    name: 'scroll_depth',
    description: 'User scrolls deep into content',
    threshold: 75, // 75% scroll
    cooldown: 6 * 60 * 60 * 1000, // 6 hours
    maxShows: 2
  },
  FORM_ABANDONMENT: {
    name: 'form_abandonment',
    description: 'User starts but doesn\'t complete form',
    cooldown: 30 * 60 * 1000, // 30 minutes
    maxShows: 1
  },
  RETURN_VISITOR: {
    name: 'return_visitor',
    description: 'User returns without converting',
    threshold: 2, // 2nd visit
    cooldown: 48 * 60 * 60 * 1000, // 48 hours
    maxShows: 1
  },
  HIGH_VALUE_PAGE: {
    name: 'high_value_page',
    description: 'User visits high-value pages multiple times',
    threshold: 3, // 3 visits to high-value pages
    cooldown: 24 * 60 * 60 * 1000, // 24 hours
    maxShows: 2
  }
};

// Campaign templates for different scenarios
export const CAMPAIGN_TEMPLATES = {
  LEAD_CAPTURE: {
    type: 'lead_capture',
    title: 'Get Your Free Space Research Consultation',
    description: 'Speak with our experts about your research goals',
    offer: 'Free 30-minute consultation',
    cta: 'Schedule Now',
    fields: ['name', 'email', 'company', 'research_area'],
    incentive: 'consultation'
  },
  CONTENT_DOWNLOAD: {
    type: 'content_download',
    title: 'Download Our Space Research Guide',
    description: 'Comprehensive guide to microgravity research opportunities',
    offer: 'Free Research Guide (PDF)',
    cta: 'Download Now',
    fields: ['name', 'email', 'company'],
    incentive: 'content'
  },
  NEWSLETTER_SIGNUP: {
    type: 'newsletter',
    title: 'Stay Updated on Space Research',
    description: 'Get the latest insights and opportunities delivered to your inbox',
    offer: 'Monthly research newsletter',
    cta: 'Subscribe',
    fields: ['email'],
    incentive: 'newsletter'
  },
  DEMO_REQUEST: {
    type: 'demo_request',
    title: 'See Our Platform in Action',
    description: 'Get a personalized demo of our research capabilities',
    offer: 'Live platform demo',
    cta: 'Request Demo',
    fields: ['name', 'email', 'company', 'phone'],
    incentive: 'demo'
  },
  PRICING_INQUIRY: {
    type: 'pricing',
    title: 'Get Custom Pricing for Your Project',
    description: 'Receive a tailored quote based on your research needs',
    offer: 'Custom pricing proposal',
    cta: 'Get Quote',
    fields: ['name', 'email', 'company', 'project_type'],
    incentive: 'pricing'
  }
};

/**
 * Retargeting Engine Class
 */
class RetargetingEngine {
  constructor() {
    this.activePopups = new Set();
    this.triggerHistory = {};
    this.exitIntentListenerAdded = false;
    this.scrollDepthTracked = false;
    this.timeOnSiteTracked = false;
    
    this.loadTriggerHistory();
    this.initializeTracking();
  }

  /**
   * Initialize tracking for retargeting triggers
   */
  initializeTracking() {
    if (!cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.MARKETING)) {
      return;
    }

    this.setupExitIntentTracking();
    this.setupTimeOnSiteTracking();
    this.setupScrollDepthTracking();
    this.setupFormAbandonmentTracking();
  }

  /**
   * Setup exit intent tracking
   */
  setupExitIntentTracking() {
    if (this.exitIntentListenerAdded) return;

    let exitIntentTriggered = false;

    const handleMouseLeave = (e) => {
      if (e.clientY <= 0 && !exitIntentTriggered) {
        exitIntentTriggered = true;
        this.triggerCampaign('EXIT_INTENT');
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);
    this.exitIntentListenerAdded = true;
  }

  /**
   * Setup time on site tracking
   */
  setupTimeOnSiteTracking() {
    if (this.timeOnSiteTracked) return;

    setTimeout(() => {
      this.triggerCampaign('TIME_ON_SITE');
      this.timeOnSiteTracked = true;
    }, TRIGGER_CONDITIONS.TIME_ON_SITE.threshold);
  }

  /**
   * Setup scroll depth tracking
   */
  setupScrollDepthTracking() {
    if (this.scrollDepthTracked) return;

    let maxScrollDepth = 0;

    const trackScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = Math.round((scrollTop / docHeight) * 100);

      if (scrollPercent > maxScrollDepth) {
        maxScrollDepth = scrollPercent;
        
        if (scrollPercent >= TRIGGER_CONDITIONS.SCROLL_DEPTH.threshold) {
          this.triggerCampaign('SCROLL_DEPTH');
          this.scrollDepthTracked = true;
          window.removeEventListener('scroll', trackScroll);
        }
      }
    };

    window.addEventListener('scroll', trackScroll, { passive: true });
  }

  /**
   * Setup form abandonment tracking
   */
  setupFormAbandonmentTracking() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
      let formStarted = false;
      let formCompleted = false;

      // Track form interaction start
      form.addEventListener('focusin', () => {
        if (!formStarted) {
          formStarted = true;
          
          // Set timeout to check for abandonment
          setTimeout(() => {
            if (formStarted && !formCompleted) {
              this.triggerCampaign('FORM_ABANDONMENT', {
                formId: form.id || 'unknown',
                formAction: form.action
              });
            }
          }, 30000); // 30 seconds
        }
      });

      // Track form completion
      form.addEventListener('submit', () => {
        formCompleted = true;
      });
    });
  }

  /**
   * Trigger a retargeting campaign
   */
  triggerCampaign(triggerType, additionalData = {}) {
    if (!this.shouldShowCampaign(triggerType)) {
      return false;
    }

    const campaign = this.selectCampaign(triggerType, additionalData);
    if (!campaign) {
      return false;
    }

    this.showCampaign(campaign, triggerType);
    this.recordTrigger(triggerType);
    
    // Track the trigger event
    analyticsTracker.trackEvent('retargeting_triggered', {
      triggerType,
      campaignType: campaign.type,
      ...additionalData
    });

    return true;
  }

  /**
   * Check if campaign should be shown based on conditions
   */
  shouldShowCampaign(triggerType) {
    const condition = TRIGGER_CONDITIONS[triggerType];
    if (!condition) return false;

    const history = this.triggerHistory[triggerType] || { count: 0, lastShown: 0 };
    
    // Check max shows limit
    if (history.count >= condition.maxShows) {
      return false;
    }

    // Check cooldown period
    const timeSinceLastShow = Date.now() - history.lastShown;
    if (timeSinceLastShow < condition.cooldown) {
      return false;
    }

    // Check if popup is already active
    if (this.activePopups.has(triggerType)) {
      return false;
    }

    return true;
  }

  /**
   * Select appropriate campaign based on user profile
   */
  selectCampaign(triggerType, additionalData = {}) {
    const leadSummary = leadScoringEngine.getLeadSummary();
    const personalization = personalizationEngine.getPersonalizationData();
    
    // High-value leads get consultation offers
    if (leadSummary.qualification === 'HOT' || leadSummary.qualification === 'WARM') {
      return this.customizeCampaign(CAMPAIGN_TEMPLATES.LEAD_CAPTURE, personalization);
    }

    // Form abandonment gets specific recovery
    if (triggerType === 'FORM_ABANDONMENT') {
      return this.customizeCampaign(CAMPAIGN_TEMPLATES.LEAD_CAPTURE, personalization);
    }

    // High engagement users get demo offers
    if (personalization.engagementLevel === 'HIGH') {
      return this.customizeCampaign(CAMPAIGN_TEMPLATES.DEMO_REQUEST, personalization);
    }

    // Medium engagement gets content offers
    if (personalization.engagementLevel === 'MEDIUM') {
      return this.customizeCampaign(CAMPAIGN_TEMPLATES.CONTENT_DOWNLOAD, personalization);
    }

    // Default to newsletter signup
    return this.customizeCampaign(CAMPAIGN_TEMPLATES.NEWSLETTER_SIGNUP, personalization);
  }

  /**
   * Customize campaign based on personalization data
   */
  customizeCampaign(template, personalization) {
    const customized = { ...template };

    // Customize based on user segment
    if (personalization.userSegment === 'BIOTECH') {
      customized.title = customized.title.replace('Space Research', 'Biotech Research');
      customized.description = customized.description.replace('research', 'biotech research');
    } else if (personalization.userSegment === 'AEROSPACE') {
      customized.title = customized.title.replace('Space Research', 'Aerospace Research');
      customized.description = customized.description.replace('research', 'aerospace research');
    }

    // Add urgency for high-value leads
    if (personalization.engagementLevel === 'HIGH') {
      customized.urgency = 'Limited time offer';
    }

    return customized;
  }

  /**
   * Show campaign popup/modal
   */
  showCampaign(campaign, triggerType) {
    // Create popup element
    const popup = this.createPopupElement(campaign, triggerType);
    document.body.appendChild(popup);
    
    this.activePopups.add(triggerType);

    // Auto-close after 30 seconds if no interaction
    setTimeout(() => {
      this.closeCampaign(triggerType);
    }, 30000);
  }

  /**
   * Create popup DOM element
   */
  createPopupElement(campaign, triggerType) {
    const popup = document.createElement('div');
    popup.className = 'retargeting-popup';
    popup.setAttribute('data-trigger', triggerType);
    
    popup.innerHTML = `
      <div class="popup-overlay">
        <div class="popup-content">
          <button class="popup-close" onclick="window.retargetingEngine.closeCampaign('${triggerType}')">&times;</button>
          <div class="popup-header">
            <h3>${campaign.title}</h3>
            ${campaign.urgency ? `<div class="popup-urgency">${campaign.urgency}</div>` : ''}
          </div>
          <div class="popup-body">
            <p>${campaign.description}</p>
            <div class="popup-offer">${campaign.offer}</div>
            <form class="popup-form" onsubmit="window.retargetingEngine.handleFormSubmit(event, '${triggerType}', '${campaign.type}')">
              ${this.generateFormFields(campaign.fields)}
              <button type="submit" class="popup-cta">${campaign.cta}</button>
            </form>
          </div>
        </div>
      </div>
    `;

    // Add styles
    this.addPopupStyles(popup);

    return popup;
  }

  /**
   * Generate form fields HTML
   */
  generateFormFields(fields) {
    const fieldMap = {
      name: '<input type="text" name="name" placeholder="Your Name" required>',
      email: '<input type="email" name="email" placeholder="Email Address" required>',
      company: '<input type="text" name="company" placeholder="Company Name">',
      phone: '<input type="tel" name="phone" placeholder="Phone Number">',
      research_area: '<select name="research_area"><option value="">Research Area</option><option value="biotech">Biotechnology</option><option value="pharma">Pharmaceutical</option><option value="materials">Materials Science</option><option value="other">Other</option></select>',
      project_type: '<select name="project_type"><option value="">Project Type</option><option value="payload">Payload Development</option><option value="mission">Full Mission</option><option value="consultation">Consultation</option></select>'
    };

    return fields.map(field => fieldMap[field] || '').join('');
  }

  /**
   * Add popup styles
   */
  addPopupStyles(popup) {
    const style = document.createElement('style');
    style.textContent = `
      .retargeting-popup {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10001;
        animation: fadeIn 0.3s ease-out;
      }
      
      .popup-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }
      
      .popup-content {
        background: white;
        border-radius: 12px;
        max-width: 500px;
        width: 100%;
        position: relative;
        animation: slideUp 0.3s ease-out;
      }
      
      .popup-close {
        position: absolute;
        top: 15px;
        right: 20px;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
      }
      
      .popup-header {
        padding: 30px 30px 20px;
        text-align: center;
      }
      
      .popup-header h3 {
        margin: 0 0 10px;
        color: #333;
        font-size: 1.5rem;
      }
      
      .popup-urgency {
        color: #ff4444;
        font-weight: bold;
        font-size: 0.9rem;
      }
      
      .popup-body {
        padding: 0 30px 30px;
      }
      
      .popup-body p {
        text-align: center;
        color: #666;
        margin-bottom: 20px;
      }
      
      .popup-offer {
        background: #f0f8ff;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
        color: #0066cc;
        margin-bottom: 20px;
      }
      
      .popup-form input,
      .popup-form select {
        width: 100%;
        padding: 12px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
      }
      
      .popup-cta {
        width: 100%;
        padding: 15px;
        background: #00d4ff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: background 0.2s;
      }
      
      .popup-cta:hover {
        background: #00b8e6;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      @keyframes slideUp {
        from { transform: translateY(50px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
    `;
    
    document.head.appendChild(style);
  }

  /**
   * Handle form submission
   */
  handleFormSubmit(event, triggerType, campaignType) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());
    
    // Track conversion
    analyticsTracker.trackEvent('retargeting_conversion', {
      triggerType,
      campaignType,
      formData: data
    });

    // Update lead scoring
    leadScoringEngine.updateCompanyProfile(data);
    
    // Close popup
    this.closeCampaign(triggerType);
    
    // Show success message
    this.showSuccessMessage(campaignType);
  }

  /**
   * Close campaign popup
   */
  closeCampaign(triggerType) {
    const popup = document.querySelector(`[data-trigger="${triggerType}"]`);
    if (popup) {
      popup.remove();
    }
    
    this.activePopups.delete(triggerType);
  }

  /**
   * Show success message
   */
  showSuccessMessage(campaignType) {
    // Implementation would show a success notification
    console.log(`Success: ${campaignType} conversion completed`);
  }

  /**
   * Record trigger in history
   */
  recordTrigger(triggerType) {
    if (!this.triggerHistory[triggerType]) {
      this.triggerHistory[triggerType] = { count: 0, lastShown: 0 };
    }
    
    this.triggerHistory[triggerType].count += 1;
    this.triggerHistory[triggerType].lastShown = Date.now();
    
    this.saveTriggerHistory();
  }

  /**
   * Load trigger history from cookies
   */
  loadTriggerHistory() {
    const history = cookieManager.getCookie('RETARGETING_HISTORY');
    this.triggerHistory = history || {};
  }

  /**
   * Save trigger history to cookies
   */
  saveTriggerHistory() {
    if (cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.MARKETING)) {
      cookieManager.setCookie('RETARGETING_HISTORY', this.triggerHistory);
    }
  }
}

// Create singleton instance and expose globally
const retargetingEngine = new RetargetingEngine();
window.retargetingEngine = retargetingEngine;

export default retargetingEngine;
