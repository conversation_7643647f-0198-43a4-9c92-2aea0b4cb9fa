/**
 * Lead Scoring and Sales Intelligence System for ResearchSat
 * Identifies high-value prospects and optimizes conversion opportunities
 */

import cookieManager, { COOKIE_CATEGORIES } from './cookieManager.js';
import analyticsTracker from './analyticsTracker.js';

// Lead scoring weights
export const SCORING_WEIGHTS = {
  // Page visit scores
  PAGES: {
    '/contact': 25,
    '/book-mission': 30,
    '/payloads': 20,
    '/partnerships': 25,
    '/missions': 15,
    '/spacexperiment': 20,
    '/about': 10,
    '/features': 12,
    '/careers': 5
  },
  
  // Engagement actions
  ACTIONS: {
    FORM_SUBMISSION: 50,
    CAL_BOOKING_OPENED: 40,
    CAL_BOOKING_COMPLETED: 100,
    EMAIL_SIGNUP: 30,
    DOWNLOAD: 20,
    DEEP_SCROLL: 10,
    RETURN_VISIT: 15,
    LONG_SESSION: 20
  },
  
  // Company indicators (detected from email domain or form data)
  COMPANY_SIZE: {
    ENTERPRISE: 50,    // >1000 employees
    LARGE: 30,         // 200-1000 employees  
    MEDIUM: 20,        // 50-200 employees
    SMALL: 10,         // <50 employees
    UNKNOWN: 0
  },
  
  // Industry relevance
  INDUSTRY: {
    BIOTECH: 40,
    PHARMACEUTICAL: 45,
    AEROSPACE: 35,
    RESEARCH_INSTITUTION: 30,
    UNIVERSITY: 25,
    GOVERNMENT: 35,
    STARTUP: 20,
    OTHER: 10
  }
};

// Company domain patterns for automatic classification
export const COMPANY_PATTERNS = {
  BIOTECH: ['biotech', 'bio', 'genetics', 'genomics', 'pharma'],
  PHARMACEUTICAL: ['pharma', 'pharmaceutical', 'drug', 'medicine'],
  AEROSPACE: ['aerospace', 'space', 'aviation', 'satellite'],
  RESEARCH_INSTITUTION: ['research', 'institute', 'lab', 'laboratory'],
  UNIVERSITY: ['edu', 'university', 'college', 'academic'],
  GOVERNMENT: ['gov', 'government', 'nasa', 'esa', 'agency']
};

// Email domain to company size mapping (simplified)
export const DOMAIN_COMPANY_SIZE = {
  // Known large companies
  'nasa.gov': 'ENTERPRISE',
  'spacex.com': 'ENTERPRISE', 
  'boeing.com': 'ENTERPRISE',
  'pfizer.com': 'ENTERPRISE',
  'novartis.com': 'ENTERPRISE',
  'roche.com': 'ENTERPRISE',
  // Add more as needed
};

/**
 * Lead Scoring Engine
 */
class LeadScoringEngine {
  constructor() {
    this.currentScore = 0;
    this.leadProfile = null;
    this.loadLeadProfile();
  }

  /**
   * Calculate comprehensive lead score
   */
  calculateLeadScore() {
    if (!cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) {
      return { score: 0, factors: {} };
    }

    let totalScore = 0;
    const factors = {};

    // 1. Page visit scoring
    const userJourney = cookieManager.getCookie('USER_JOURNEY');
    if (userJourney?.pages) {
      const pageScores = this.calculatePageScores(userJourney.pages);
      totalScore += pageScores.total;
      factors.pageVisits = pageScores;
    }

    // 2. Engagement scoring
    const engagementData = cookieManager.getCookie('ENGAGEMENT_SCORE');
    if (engagementData) {
      totalScore += Math.min(engagementData.score, 200); // Cap engagement contribution
      factors.engagement = engagementData.score;
    }

    // 3. Action-based scoring
    const sessionData = cookieManager.getCookie('SESSION_ID');
    if (sessionData?.events) {
      const actionScores = this.calculateActionScores(sessionData.events);
      totalScore += actionScores.total;
      factors.actions = actionScores;
    }

    // 4. Company profile scoring
    const companyProfile = this.getCompanyProfile();
    if (companyProfile) {
      const companyScore = this.calculateCompanyScore(companyProfile);
      totalScore += companyScore.total;
      factors.company = companyScore;
    }

    // 5. Behavioral indicators
    const behaviorScore = this.calculateBehaviorScore();
    totalScore += behaviorScore.total;
    factors.behavior = behaviorScore;

    // 6. Recency and frequency
    const recencyScore = this.calculateRecencyScore(userJourney);
    totalScore += recencyScore.total;
    factors.recency = recencyScore;

    this.currentScore = Math.min(totalScore, 1000); // Cap at 1000

    // Update lead profile
    this.updateLeadProfile({
      score: this.currentScore,
      factors,
      lastCalculated: new Date().toISOString()
    });

    return {
      score: this.currentScore,
      factors,
      qualification: this.getLeadQualification(this.currentScore),
      recommendations: this.getRecommendations(factors)
    };
  }

  /**
   * Calculate page visit scores
   */
  calculatePageScores(pages) {
    const pageVisits = {};
    let total = 0;

    pages.forEach(page => {
      const score = SCORING_WEIGHTS.PAGES[page.path] || 0;
      pageVisits[page.path] = (pageVisits[page.path] || 0) + score;
      total += score;
    });

    // Bonus for visiting multiple high-value pages
    const highValuePages = Object.keys(pageVisits).filter(path => 
      SCORING_WEIGHTS.PAGES[path] >= 20
    );
    if (highValuePages.length >= 3) {
      total += 25; // Diversity bonus
    }

    return { total, breakdown: pageVisits, diversity: highValuePages.length };
  }

  /**
   * Calculate action-based scores
   */
  calculateActionScores(events) {
    const actions = {};
    let total = 0;

    events.forEach(event => {
      let score = 0;
      
      switch (event.type) {
        case 'form_submission':
          score = SCORING_WEIGHTS.ACTIONS.FORM_SUBMISSION;
          break;
        case 'cal_booking':
          score = event.action === 'completed' 
            ? SCORING_WEIGHTS.ACTIONS.CAL_BOOKING_COMPLETED
            : SCORING_WEIGHTS.ACTIONS.CAL_BOOKING_OPENED;
          break;
        case 'email_signup':
          score = SCORING_WEIGHTS.ACTIONS.EMAIL_SIGNUP;
          break;
        case 'scroll_depth':
          if (event.depth >= 75) {
            score = SCORING_WEIGHTS.ACTIONS.DEEP_SCROLL;
          }
          break;
        case 'time_on_page':
          if (event.duration > 120000) { // 2+ minutes
            score = SCORING_WEIGHTS.ACTIONS.LONG_SESSION;
          }
          break;
      }

      if (score > 0) {
        actions[event.type] = (actions[event.type] || 0) + score;
        total += score;
      }
    });

    return { total, breakdown: actions };
  }

  /**
   * Calculate company profile score
   */
  calculateCompanyScore(companyProfile) {
    let total = 0;
    const breakdown = {};

    // Industry score
    if (companyProfile.industry) {
      const industryScore = SCORING_WEIGHTS.INDUSTRY[companyProfile.industry] || 0;
      total += industryScore;
      breakdown.industry = industryScore;
    }

    // Company size score
    if (companyProfile.size) {
      const sizeScore = SCORING_WEIGHTS.COMPANY_SIZE[companyProfile.size] || 0;
      total += sizeScore;
      breakdown.size = sizeScore;
    }

    // Email domain authority
    if (companyProfile.emailDomain) {
      const domainScore = this.calculateDomainScore(companyProfile.emailDomain);
      total += domainScore;
      breakdown.domain = domainScore;
    }

    return { total, breakdown };
  }

  /**
   * Calculate behavioral indicators
   */
  calculateBehaviorScore() {
    let total = 0;
    const breakdown = {};

    const userJourney = cookieManager.getCookie('USER_JOURNEY');
    if (userJourney) {
      // Return visitor bonus
      if (userJourney.totalVisits > 1) {
        const returnScore = Math.min(userJourney.totalVisits * 5, 30);
        total += returnScore;
        breakdown.returnVisitor = returnScore;
      }

      // Session length bonus
      const sessionData = cookieManager.getCookie('SESSION_ID');
      if (sessionData?.events) {
        const sessionLength = sessionData.events.length;
        if (sessionLength > 10) {
          const lengthScore = Math.min(sessionLength * 2, 20);
          total += lengthScore;
          breakdown.sessionLength = lengthScore;
        }
      }
    }

    return { total, breakdown };
  }

  /**
   * Calculate recency and frequency scores
   */
  calculateRecencyScore(userJourney) {
    let total = 0;
    const breakdown = {};

    if (userJourney?.lastVisit) {
      const daysSinceLastVisit = (Date.now() - new Date(userJourney.lastVisit)) / (1000 * 60 * 60 * 24);
      
      // Recent activity bonus
      if (daysSinceLastVisit < 1) {
        total += 15;
        breakdown.recentActivity = 15;
      } else if (daysSinceLastVisit < 7) {
        total += 10;
        breakdown.recentActivity = 10;
      }
    }

    return { total, breakdown };
  }

  /**
   * Calculate domain authority score
   */
  calculateDomainScore(domain) {
    // Known enterprise domains
    if (DOMAIN_COMPANY_SIZE[domain]) {
      return 20;
    }

    // Government/edu domains
    if (domain.endsWith('.gov') || domain.endsWith('.edu')) {
      return 15;
    }

    // Common business domains
    if (domain.endsWith('.com') || domain.endsWith('.org')) {
      return 5;
    }

    return 0;
  }

  /**
   * Get lead qualification level
   */
  getLeadQualification(score) {
    if (score >= 300) return 'HOT';
    if (score >= 200) return 'WARM';
    if (score >= 100) return 'QUALIFIED';
    if (score >= 50) return 'INTERESTED';
    return 'COLD';
  }

  /**
   * Get personalized recommendations
   */
  getRecommendations(factors) {
    const recommendations = [];

    // High engagement, no contact
    if (factors.engagement > 100 && !factors.actions?.form_submission) {
      recommendations.push({
        type: 'CONTACT_FORM',
        priority: 'HIGH',
        message: 'Show contact form popup - high engagement detected'
      });
    }

    // Visited payloads but no booking
    if (factors.pageVisits?.breakdown?.['/payloads'] && !factors.actions?.cal_booking) {
      recommendations.push({
        type: 'CAL_BOOKING',
        priority: 'MEDIUM',
        message: 'Promote consultation booking - payload interest detected'
      });
    }

    // Return visitor without conversion
    if (factors.behavior?.returnVisitor && factors.score < 200) {
      recommendations.push({
        type: 'RETARGETING',
        priority: 'MEDIUM',
        message: 'Target with email campaign - return visitor'
      });
    }

    return recommendations;
  }

  /**
   * Update company profile from form data
   */
  updateCompanyProfile(formData) {
    if (!cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.PERSONALIZATION)) return;

    let profile = cookieManager.getCookie('COMPANY_PROFILE') || {};

    // Extract company info from form
    if (formData.email) {
      const domain = formData.email.split('@')[1];
      profile.emailDomain = domain;
      profile.industry = this.detectIndustryFromDomain(domain);
      profile.size = DOMAIN_COMPANY_SIZE[domain] || 'UNKNOWN';
    }

    if (formData.organization || formData.company) {
      profile.companyName = formData.organization || formData.company;
    }

    profile.lastUpdated = new Date().toISOString();
    cookieManager.setCookie('COMPANY_PROFILE', profile);
  }

  /**
   * Detect industry from email domain
   */
  detectIndustryFromDomain(domain) {
    for (const [industry, patterns] of Object.entries(COMPANY_PATTERNS)) {
      if (patterns.some(pattern => domain.toLowerCase().includes(pattern))) {
        return industry;
      }
    }
    return 'OTHER';
  }

  /**
   * Get current company profile
   */
  getCompanyProfile() {
    return cookieManager.getCookie('COMPANY_PROFILE');
  }

  /**
   * Load lead profile
   */
  loadLeadProfile() {
    this.leadProfile = cookieManager.getCookie('LEAD_PROFILE') || {
      score: 0,
      qualification: 'COLD',
      firstSeen: new Date().toISOString()
    };
  }

  /**
   * Update lead profile
   */
  updateLeadProfile(data) {
    this.leadProfile = {
      ...this.leadProfile,
      ...data,
      lastUpdated: new Date().toISOString()
    };

    if (cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) {
      cookieManager.setCookie('LEAD_PROFILE', this.leadProfile);
    }
  }

  /**
   * Get lead summary for sales team
   */
  getLeadSummary() {
    const scoring = this.calculateLeadScore();
    const companyProfile = this.getCompanyProfile();
    const userJourney = cookieManager.getCookie('USER_JOURNEY');
    const sessionData = cookieManager.getCookie('SESSION_ID');

    return {
      score: scoring.score,
      qualification: scoring.qualification,
      companyProfile,
      visitHistory: userJourney?.pages?.slice(-10), // Last 10 pages
      totalVisits: userJourney?.totalVisits || 0,
      lastActivity: sessionData?.lastActivity,
      recommendations: scoring.recommendations,
      factors: scoring.factors
    };
  }
}

// Create singleton instance
const leadScoringEngine = new LeadScoringEngine();

export default leadScoringEngine;
