/**
 * Advanced Cookie Management System for ResearchSat
 * Handles GDPR compliance, analytics, and sales optimization
 */

// Cookie categories for consent management
export const COOKIE_CATEGORIES = {
  ESSENTIAL: 'essential',
  ANALYTICS: 'analytics', 
  MARKETING: 'marketing',
  PERSONALIZATION: 'personalization'
};

// Cookie configuration with expiration and category
export const COOKIE_CONFIG = {
  // Essential cookies (always allowed)
  USER_PREFERENCES: {
    name: 'researchsat_preferences',
    category: COOKIE_CATEGORIES.ESSENTIAL,
    expireDays: 365,
    description: 'Stores user preferences and settings'
  },
  CONSENT_PREFERENCES: {
    name: 'researchsat_consent',
    category: COOKIE_CATEGORIES.ESSENTIAL,
    expireDays: 365,
    description: 'Stores cookie consent preferences'
  },
  
  // Analytics cookies
  SESSION_ID: {
    name: 'researchsat_session',
    category: COOKIE_CATEGORIES.ANALYTICS,
    expireDays: 1,
    description: 'Tracks user session for analytics'
  },
  USER_JOURNEY: {
    name: 'researchsat_journey',
    category: COOKIE_CATEGORIES.ANALYTICS,
    expireDays: 30,
    description: 'Tracks user journey across visits'
  },
  ENGAGEMENT_SCORE: {
    name: 'researchsat_engagement',
    category: COOKIE_CATEGORIES.ANALYTICS,
    expireDays: 90,
    description: 'Calculates user engagement score'
  },
  
  // Marketing cookies
  LEAD_SOURCE: {
    name: 'researchsat_source',
    category: COOKIE_CATEGORIES.MARKETING,
    expireDays: 30,
    description: 'Tracks lead source and campaign data'
  },
  RETARGETING: {
    name: 'researchsat_retarget',
    category: COOKIE_CATEGORIES.MARKETING,
    expireDays: 90,
    description: 'Enables retargeting campaigns'
  },
  
  // Personalization cookies
  INTEREST_PROFILE: {
    name: 'researchsat_interests',
    category: COOKIE_CATEGORIES.PERSONALIZATION,
    expireDays: 180,
    description: 'Stores user interest profile for personalization'
  },
  COMPANY_PROFILE: {
    name: 'researchsat_company',
    category: COOKIE_CATEGORIES.PERSONALIZATION,
    expireDays: 365,
    description: 'Stores company information for B2B targeting'
  }
};

/**
 * Cookie utility class for managing all cookie operations
 */
class CookieManager {
  constructor() {
    this.consentGiven = this.getConsentPreferences();
    this.sessionId = this.generateSessionId();
  }

  /**
   * Set a cookie with proper configuration
   */
  setCookie(cookieKey, value, options = {}) {
    const config = COOKIE_CONFIG[cookieKey];
    if (!config) {
      console.warn(`Unknown cookie key: ${cookieKey}`);
      return false;
    }

    // Check if consent is given for this category
    if (!this.hasConsentForCategory(config.category)) {
      console.log(`Consent not given for ${config.category} cookies`);
      return false;
    }

    const expires = new Date();
    expires.setTime(expires.getTime() + (config.expireDays * 24 * 60 * 60 * 1000));
    
    const cookieString = `${config.name}=${encodeURIComponent(JSON.stringify(value))}; expires=${expires.toUTCString()}; path=/; SameSite=Lax${options.secure ? '; Secure' : ''}`;
    
    document.cookie = cookieString;
    return true;
  }

  /**
   * Get a cookie value
   */
  getCookie(cookieKey) {
    const config = COOKIE_CONFIG[cookieKey];
    if (!config) return null;

    const name = config.name + "=";
    const decodedCookie = decodeURIComponent(document.cookie);
    const ca = decodedCookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') {
        c = c.substring(1);
      }
      if (c.indexOf(name) === 0) {
        try {
          return JSON.parse(c.substring(name.length, c.length));
        } catch (e) {
          return c.substring(name.length, c.length);
        }
      }
    }
    return null;
  }

  /**
   * Delete a cookie
   */
  deleteCookie(cookieKey) {
    const config = COOKIE_CONFIG[cookieKey];
    if (!config) return;

    document.cookie = `${config.name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  /**
   * Check if consent is given for a specific category
   */
  hasConsentForCategory(category) {
    if (category === COOKIE_CATEGORIES.ESSENTIAL) return true;
    
    const consent = this.getConsentPreferences();
    return consent[category] === true;
  }

  /**
   * Get current consent preferences
   */
  getConsentPreferences() {
    const consentCookie = this.getRawCookie('researchsat_consent');
    if (!consentCookie) {
      return {
        [COOKIE_CATEGORIES.ESSENTIAL]: true,
        [COOKIE_CATEGORIES.ANALYTICS]: false,
        [COOKIE_CATEGORIES.MARKETING]: false,
        [COOKIE_CATEGORIES.PERSONALIZATION]: false,
        timestamp: null
      };
    }
    
    try {
      return JSON.parse(consentCookie);
    } catch (e) {
      return {
        [COOKIE_CATEGORIES.ESSENTIAL]: true,
        [COOKIE_CATEGORIES.ANALYTICS]: false,
        [COOKIE_CATEGORIES.MARKETING]: false,
        [COOKIE_CATEGORIES.PERSONALIZATION]: false,
        timestamp: null
      };
    }
  }

  /**
   * Set consent preferences
   */
  setConsentPreferences(preferences) {
    const consentData = {
      ...preferences,
      timestamp: new Date().toISOString()
    };
    
    const expires = new Date();
    expires.setTime(expires.getTime() + (365 * 24 * 60 * 60 * 1000));
    
    document.cookie = `researchsat_consent=${encodeURIComponent(JSON.stringify(consentData))}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
    
    this.consentGiven = consentData;
    
    // Clean up cookies for categories where consent was withdrawn
    this.cleanupNonConsentedCookies(preferences);
  }

  /**
   * Clean up cookies for categories where consent was withdrawn
   */
  cleanupNonConsentedCookies(newConsent) {
    Object.entries(COOKIE_CONFIG).forEach(([key, config]) => {
      if (config.category !== COOKIE_CATEGORIES.ESSENTIAL && !newConsent[config.category]) {
        this.deleteCookie(key);
      }
    });
  }

  /**
   * Get raw cookie value (for internal use)
   */
  getRawCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
    return null;
  }

  /**
   * Generate unique session ID
   */
  generateSessionId() {
    return 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Initialize session tracking
   */
  initializeSession() {
    if (this.hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) {
      const existingSession = this.getCookie('SESSION_ID');
      if (!existingSession) {
        this.setCookie('SESSION_ID', {
          id: this.sessionId,
          startTime: new Date().toISOString(),
          pageViews: 0,
          events: []
        });
      }
    }
  }

  /**
   * Get all cookies for a specific category
   */
  getCookiesByCategory(category) {
    return Object.entries(COOKIE_CONFIG)
      .filter(([key, config]) => config.category === category)
      .map(([key, config]) => ({
        key,
        name: config.name,
        value: this.getCookie(key),
        description: config.description
      }));
  }

  /**
   * Clear all cookies (for testing/reset)
   */
  clearAllCookies() {
    Object.keys(COOKIE_CONFIG).forEach(key => {
      this.deleteCookie(key);
    });
  }
}

// Create singleton instance
const cookieManager = new CookieManager();

export default cookieManager;
