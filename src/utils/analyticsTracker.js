/**
 * Advanced Analytics Tracking System for ResearchSat
 * Tracks user behavior, engagement, and sales funnel progression
 */

import cookieManager, { COOKIE_CATEGORIES } from './cookieManager.js';

// Event types for tracking
export const EVENT_TYPES = {
  PAGE_VIEW: 'page_view',
  BUTTON_CLICK: 'button_click',
  FORM_INTERACTION: 'form_interaction',
  FORM_SUBMISSION: 'form_submission',
  SCROLL_DEPTH: 'scroll_depth',
  TIME_ON_PAGE: 'time_on_page',
  CAL_BOOKING: 'cal_booking',
  DOWNLOAD: 'download',
  EMAIL_SIGNUP: 'email_signup',
  CONTACT_ATTEMPT: 'contact_attempt',
  INTEREST_SIGNAL: 'interest_signal'
};

// High-value pages for lead scoring
export const HIGH_VALUE_PAGES = {
  '/payloads': 10,
  '/missions': 8,
  '/contact': 15,
  '/book-mission': 20,
  '/partnerships': 12,
  '/about': 5,
  '/features': 7
};

// Interest categories based on page visits
export const INTEREST_CATEGORIES = {
  PAYLOADS: ['payloads', 'features'],
  MISSIONS: ['missions', 'past-missions'],
  PARTNERSHIPS: ['partnerships', 'contact'],
  RESEARCH: ['about', 'news'],
  COMMERCIAL: ['book-mission', 'spacexperiment']
};

/**
 * Analytics Tracker Class
 */
class AnalyticsTracker {
  constructor() {
    this.sessionStartTime = Date.now();
    this.currentPageStartTime = Date.now();
    this.scrollDepthTracked = new Set();
    this.engagementScore = 0;
    this.userJourney = [];
    
    this.initializeTracking();
  }

  /**
   * Initialize tracking systems
   */
  initializeTracking() {
    if (cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) {
      this.initializeSession();
      this.setupScrollTracking();
      this.setupEngagementTracking();
      this.loadUserJourney();
    }
  }

  /**
   * Initialize session tracking
   */
  initializeSession() {
    cookieManager.initializeSession();
    
    // Track session data
    const sessionData = cookieManager.getCookie('SESSION_ID') || {
      id: cookieManager.sessionId,
      startTime: new Date().toISOString(),
      pageViews: 0,
      events: []
    };

    sessionData.lastActivity = new Date().toISOString();
    cookieManager.setCookie('SESSION_ID', sessionData);
  }

  /**
   * Track page view with enhanced data
   */
  trackPageView(path, additionalData = {}) {
    if (!cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) return;

    const pageViewData = {
      type: EVENT_TYPES.PAGE_VIEW,
      path,
      timestamp: new Date().toISOString(),
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
      ...additionalData
    };

    // Update session data
    const sessionData = cookieManager.getCookie('SESSION_ID');
    if (sessionData) {
      sessionData.pageViews += 1;
      sessionData.events.push(pageViewData);
      sessionData.lastActivity = new Date().toISOString();
      cookieManager.setCookie('SESSION_ID', sessionData);
    }

    // Update user journey
    this.updateUserJourney(path);
    
    // Update engagement score
    this.updateEngagementScore(path);
    
    // Update interest profile
    this.updateInterestProfile(path);

    // Reset page timing
    this.currentPageStartTime = Date.now();
    this.scrollDepthTracked.clear();

    console.log('Page view tracked:', pageViewData);
  }

  /**
   * Track custom events
   */
  trackEvent(eventType, eventData = {}) {
    if (!cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) return;

    const event = {
      type: eventType,
      timestamp: new Date().toISOString(),
      path: window.location.pathname,
      ...eventData
    };

    // Add to session events
    const sessionData = cookieManager.getCookie('SESSION_ID');
    if (sessionData) {
      sessionData.events.push(event);
      sessionData.lastActivity = new Date().toISOString();
      cookieManager.setCookie('SESSION_ID', sessionData);
    }

    // Update engagement score for high-value events
    if ([EVENT_TYPES.FORM_SUBMISSION, EVENT_TYPES.CAL_BOOKING, EVENT_TYPES.EMAIL_SIGNUP].includes(eventType)) {
      this.updateEngagementScore(null, 25);
    }

    console.log('Event tracked:', event);
  }

  /**
   * Track form interactions
   */
  trackFormInteraction(formId, field, action) {
    this.trackEvent(EVENT_TYPES.FORM_INTERACTION, {
      formId,
      field,
      action, // 'focus', 'blur', 'change'
      value: action === 'change' ? 'filled' : null
    });
  }

  /**
   * Track form submissions
   */
  trackFormSubmission(formId, formData = {}) {
    this.trackEvent(EVENT_TYPES.FORM_SUBMISSION, {
      formId,
      fields: Object.keys(formData),
      hasEmail: !!formData.email,
      hasPhone: !!formData.phone,
      hasCompany: !!formData.organization || !!formData.company
    });

    // High-value conversion event
    this.updateEngagementScore(null, 50);
  }

  /**
   * Track Cal.com booking attempts
   */
  trackCalBooking(calLink, action = 'opened') {
    this.trackEvent(EVENT_TYPES.CAL_BOOKING, {
      calLink,
      action, // 'opened', 'completed', 'abandoned'
    });

    if (action === 'completed') {
      this.updateEngagementScore(null, 100); // Highest value conversion
    }
  }

  /**
   * Setup scroll depth tracking
   */
  setupScrollTracking() {
    let ticking = false;

    const trackScrollDepth = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = Math.round((scrollTop / docHeight) * 100);

      // Track at 25%, 50%, 75%, 90% intervals
      const milestones = [25, 50, 75, 90];
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !this.scrollDepthTracked.has(milestone)) {
          this.scrollDepthTracked.add(milestone);
          this.trackEvent(EVENT_TYPES.SCROLL_DEPTH, {
            depth: milestone,
            path: window.location.pathname
          });
          
          // Add engagement points for deep scrolling
          this.updateEngagementScore(null, milestone / 10);
        }
      });

      ticking = false;
    };

    const requestScrollTrack = () => {
      if (!ticking) {
        requestAnimationFrame(trackScrollDepth);
        ticking = true;
      }
    };

    window.addEventListener('scroll', requestScrollTrack, { passive: true });
  }

  /**
   * Setup engagement tracking (time on page, clicks, etc.)
   */
  setupEngagementTracking() {
    // Track time on page when user leaves
    window.addEventListener('beforeunload', () => {
      const timeOnPage = Date.now() - this.currentPageStartTime;
      this.trackEvent(EVENT_TYPES.TIME_ON_PAGE, {
        duration: timeOnPage,
        path: window.location.pathname
      });
    });

    // Track clicks on important elements
    document.addEventListener('click', (event) => {
      const target = event.target.closest('a, button, [data-track]');
      if (target) {
        const trackingData = {
          element: target.tagName.toLowerCase(),
          text: target.textContent?.trim().substring(0, 50),
          href: target.href,
          className: target.className,
          dataTrack: target.getAttribute('data-track')
        };

        this.trackEvent(EVENT_TYPES.BUTTON_CLICK, trackingData);

        // Special tracking for high-value clicks
        if (target.href?.includes('cal.com') || target.getAttribute('data-cal-link')) {
          this.trackCalBooking(target.getAttribute('data-cal-link') || target.href, 'opened');
        }
      }
    });
  }

  /**
   * Update user journey tracking
   */
  updateUserJourney(path) {
    let journey = cookieManager.getCookie('USER_JOURNEY') || {
      pages: [],
      firstVisit: new Date().toISOString(),
      totalVisits: 0
    };

    journey.pages.push({
      path,
      timestamp: new Date().toISOString(),
      sessionId: cookieManager.sessionId
    });

    // Keep only last 50 page views
    if (journey.pages.length > 50) {
      journey.pages = journey.pages.slice(-50);
    }

    journey.lastVisit = new Date().toISOString();
    journey.totalVisits += 1;

    cookieManager.setCookie('USER_JOURNEY', journey);
    this.userJourney = journey;
  }

  /**
   * Update engagement score
   */
  updateEngagementScore(path = null, additionalPoints = 0) {
    let currentScore = cookieManager.getCookie('ENGAGEMENT_SCORE') || {
      score: 0,
      lastUpdated: new Date().toISOString(),
      factors: {}
    };

    // Add points for high-value pages
    if (path && HIGH_VALUE_PAGES[path]) {
      currentScore.score += HIGH_VALUE_PAGES[path];
      currentScore.factors[path] = (currentScore.factors[path] || 0) + HIGH_VALUE_PAGES[path];
    }

    // Add additional points
    currentScore.score += additionalPoints;
    currentScore.lastUpdated = new Date().toISOString();

    // Cap score at 1000
    currentScore.score = Math.min(currentScore.score, 1000);

    cookieManager.setCookie('ENGAGEMENT_SCORE', currentScore);
    this.engagementScore = currentScore.score;
  }

  /**
   * Update interest profile based on page visits
   */
  updateInterestProfile(path) {
    if (!cookieManager.hasConsentForCategory(COOKIE_CATEGORIES.PERSONALIZATION)) return;

    let interests = cookieManager.getCookie('INTEREST_PROFILE') || {
      categories: {},
      lastUpdated: new Date().toISOString()
    };

    // Determine interest category
    Object.entries(INTEREST_CATEGORIES).forEach(([category, pages]) => {
      if (pages.some(page => path.includes(page))) {
        interests.categories[category] = (interests.categories[category] || 0) + 1;
      }
    });

    interests.lastUpdated = new Date().toISOString();
    cookieManager.setCookie('INTEREST_PROFILE', interests);
  }

  /**
   * Load existing user journey
   */
  loadUserJourney() {
    this.userJourney = cookieManager.getCookie('USER_JOURNEY') || { pages: [] };
  }

  /**
   * Get analytics summary
   */
  getAnalyticsSummary() {
    return {
      sessionId: cookieManager.sessionId,
      engagementScore: this.engagementScore,
      userJourney: this.userJourney,
      sessionData: cookieManager.getCookie('SESSION_ID'),
      interestProfile: cookieManager.getCookie('INTEREST_PROFILE'),
      consentGiven: cookieManager.getConsentPreferences()
    };
  }
}

// Create singleton instance
const analyticsTracker = new AnalyticsTracker();

export default analyticsTracker;
