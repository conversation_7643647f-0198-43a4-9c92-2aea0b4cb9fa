import React, { useCallback } from 'react';
import { Link } from 'react-router-dom';
import SEO from '../components/SEO';
import SectionDivider from '../components/SectionDivider';
import styles from '../styles/pages/MicrogravityGuide.module.css';
import logoImage from '../assets/images/ResearchSatLogo.png';
import avatarImg from '../assets/images/news/avatar-square.svg';
import heroImage from '../assets/images/features/spacemissionResearch.png';
import analyticsTracker from '../utils/analyticsTracker';
import leadScoringEngine from '../utils/leadScoring';

const MicrogravityGuide = () => {
  // Download handler for PDF
  const handleDownload = useCallback(() => {
    try {
      // Track PDF download event
      analyticsTracker.trackEvent('content_download', {
        contentType: 'microgravity_guide',
        format: 'pdf',
        source: 'guide_page'
      });

      // Update lead scoring for content download
      leadScoringEngine.updateCompanyProfile({
        contentDownloaded: 'microgravity_guide',
        downloadDate: new Date().toISOString()
      });

      // Track with Google Analytics
      if (typeof gtag !== 'undefined') {
        gtag('event', 'download', {
          event_category: 'content',
          event_label: 'microgravity_guide_pdf',
          value: 20
        });
      }
    } catch (error) {
      console.error('Error tracking download:', error);
    }
  }, []);

  // Article data
  const article = {
    title: 'The Complete Guide to Microgravity Research Opportunities',
    author: 'ResearchSat Team',
    date: 'December 15, 2024',
    category: 'Research Guide',
    readTime: '15 min read',
    summary: 'Discover how microgravity research can accelerate your innovations and create competitive advantages. This comprehensive guide covers everything from life sciences to materials research in space.'
  };

  // Related articles
  const relatedArticles = [
    {
      id: 'payloads',
      title: 'Payload Development Services',
      category: 'Services',
      image: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      link: '/payloads'
    },
    {
      id: 'missions',
      title: 'Upcoming Research Missions',
      category: 'Missions',
      image: 'https://images.unsplash.com/photo-1517976487492-5750f3195933?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      link: '/missions'
    },
    {
      id: 'partnerships',
      title: 'Research Partnerships',
      category: 'Partnerships',
      image: 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80',
      link: '/partnerships'
    }
  ];

  return (
    <>
      <SEO
        title="Complete Guide to Microgravity Research Opportunities | ResearchSat"
        description="Comprehensive guide to microgravity research opportunities. Learn how space-based research can accelerate your innovations and create competitive advantages."
        keywords="microgravity research, space research, satellite experiments, life sciences, materials science, ResearchSat"
      />

      {/* Article Hero Header */}
      <section className={styles.articleHero}>
        <div className={styles.heroImageContainer}>
          <img src={heroImage} alt={article.title} className={styles.heroImage} />
          <div className={styles.heroOverlay}></div>
        </div>

        <div className={styles.heroContent}>
          <div className={styles.container}>
            <Link to="/" className={styles.backLink}>
              Back to Home
            </Link>
            <div className={styles.articleMeta}>
              <span className={styles.articleCategory}>{article.category}</span>
              <span className={styles.articleDate}>{article.date}</span>
              <span className={styles.readTime}>{article.readTime}</span>
            </div>
            <h1 className={styles.articleTitle}>{article.title}</h1>
            <div className={styles.articleAuthor}>
              <img src={logoImage} alt={article.author} className={styles.authorAvatar} />
              <span className={styles.authorName}>{article.author}</span>
            </div>
          </div>
        </div>
      </section>

      {/* Article Summary Section */}
      <section className={styles.summarySection}>
        <div className={styles.summaryContainer}>
          <div className={styles.summaryContent}>
            <div className={styles.summaryWrapper}>
              <div className={styles.summaryInner}>
                <div className={styles.summaryText}>
                  {article.summary}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className={styles.articleContent}>
        <div className={styles.container}>
          <div className={styles.contentWrapper}>
            <div className={styles.mainContent}>

              <h2>📈 Why Invest in Microgravity Research Now?</h2>

              <p>The space economy is experiencing unprecedented growth, with the global space industry valued at over $400 billion and projected to reach $1 trillion by 2040. Microgravity research represents one of the most promising frontiers for breakthrough discoveries that can transform industries on Earth.</p>

              <div className={styles.statsGrid}>
                <div className={styles.statItem}>
                  <span className={styles.statNumber}>70%</span>
                  <div className={styles.statLabel}>Reduction in launch costs over 5 years</div>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statNumber}>40%</span>
                  <div className={styles.statLabel}>Higher resolution protein crystals in space</div>
                </div>
                <div className={styles.statItem}>
                  <span className={styles.statNumber}>300%</span>
                  <div className={styles.statLabel}>Increase in space research missions</div>
                </div>
              </div>

              <h2>🧬 Life Sciences in Microgravity</h2>

              <p>Microgravity provides a unique environment for biological research that cannot be replicated on Earth. The absence of gravitational forces allows scientists to study fundamental biological processes without the confounding effects of gravity.</p>

              <h3>Protein Crystallization</h3>
              <p>Space-grown protein crystals are typically larger, more ordered, and have fewer defects than their Earth-grown counterparts. This leads to better structural data for drug development and can accelerate the discovery of new therapeutics.</p>

              <h3>Cell Culture Research</h3>
              <p>Cells behave differently in microgravity, offering insights into:</p>
              <ul>
                <li>Cancer cell growth and metastasis mechanisms</li>
                <li>Tissue engineering and regenerative medicine</li>
                <li>Aging processes and cellular senescence</li>
                <li>Immune system responses</li>
              </ul>

              <h3>Pharmaceutical Development</h3>
              <p>The unique conditions of space enable the development of new drug formulations and delivery systems that could revolutionize medicine on Earth.</p>

              <h2>🔬 Materials Science Breakthroughs</h2>

              <p>Microgravity eliminates many of the limitations that affect materials processing on Earth, enabling the creation of superior materials with enhanced properties.</p>

              <h3>Advanced Alloys and Composites</h3>
              <p>Without gravitational settling and convection, materials can be processed more uniformly, resulting in:</p>
              <ul>
                <li>Stronger and lighter aerospace materials</li>
                <li>More efficient semiconductor crystals</li>
                <li>Novel metal alloys with unique properties</li>
                <li>Advanced fiber optics with reduced defects</li>
              </ul>

              <h3>Crystal Growth</h3>
              <p>Space-based crystal growth produces materials with superior optical, electronic, and mechanical properties for applications in telecommunications, computing, and energy storage.</p>

              <h2>🚀 Implementation Roadmap</h2>

              <p>Getting started with microgravity research requires careful planning and the right partnerships. Here's your step-by-step guide:</p>

              <div className={styles.processStep}>
                <div className={styles.stepNumber}>1</div>
                <div className={styles.stepContent}>
                  <h4>Research Design & Feasibility</h4>
                  <p>Define your research objectives and assess the benefits of conducting experiments in microgravity versus terrestrial alternatives.</p>
                </div>
              </div>

              <div className={styles.processStep}>
                <div className={styles.stepNumber}>2</div>
                <div className={styles.stepContent}>
                  <h4>Payload Development</h4>
                  <p>Design and build specialized equipment that can operate autonomously in the space environment while maintaining precise experimental conditions.</p>
                </div>
              </div>

              <div className={styles.processStep}>
                <div className={styles.stepNumber}>3</div>
                <div className={styles.stepContent}>
                  <h4>Mission Integration</h4>
                  <p>Integrate your payload with satellite systems and coordinate launch schedules with mission requirements.</p>
                </div>
              </div>

              <div className={styles.processStep}>
                <div className={styles.stepNumber}>4</div>
                <div className={styles.stepContent}>
                  <h4>Data Collection & Analysis</h4>
                  <p>Monitor experiments in real-time, collect data, and analyze results to derive actionable insights for your research.</p>
                </div>
              </div>

            </div>

            <div className={styles.sidebar}>
              <div className={styles.downloadSection}>
                <h3 className={styles.sidebarTitle}>Download Complete Guide</h3>
                <p className={styles.downloadDescription}>
                  Get the full PDF version with detailed case studies, technical specifications, and implementation timelines.
                </p>
                <a
                  href="/Comprehensive-Guide-to-Microgravity-Research-Opportunities.pdf"
                  download="Comprehensive-Guide-to-Microgravity-Research-Opportunities.pdf"
                  className={styles.downloadButton}
                  onClick={handleDownload}
                  aria-label="Download PDF version of the Microgravity Research Guide"
                >
                  <span className={styles.downloadIcon}>📄</span>
                  Download Complete PDF Guide
                </a>
              </div>

              <div className={styles.shareSection}>
                <h3 className={styles.sidebarTitle}>Share This Guide</h3>
                <div className={styles.socialLinks}>
                  <a href="#" className={styles.socialLink}>Twitter</a>
                  <a href="#" className={styles.socialLink}>LinkedIn</a>
                  <a href="#" className={styles.socialLink}>Email</a>
                </div>
              </div>

              <div className={styles.tagsSection}>
                <h3 className={styles.sidebarTitle}>Topics</h3>
                <div className={styles.tagsList}>
                  <span className={styles.tag}>Microgravity</span>
                  <span className={styles.tag}>Life Sciences</span>
                  <span className={styles.tag}>Materials Science</span>
                  <span className={styles.tag}>Space Research</span>
                  <span className={styles.tag}>Biotechnology</span>
                  <span className={styles.tag}>Innovation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <SectionDivider />

      {/* Related Articles */}
      <section className={styles.relatedArticles}>
        <div className={styles.container}>
          <h2 className={styles.relatedTitle}>Explore More</h2>
          <div className={styles.relatedGrid}>
            {relatedArticles.map(article => (
              <Link to={article.link} key={article.id} className={styles.relatedCard}>
                <div className={styles.relatedImageContainer}>
                  <img src={article.image} alt={article.title} className={styles.relatedImage} />
                </div>
                <div className={styles.relatedContent}>
                  <span className={styles.relatedCategory}>{article.category}</span>
                  <h3 className={styles.relatedCardTitle}>{article.title}</h3>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      <SectionDivider />

      {/* Call to Action */}
      <section className={styles.ctaSection}>
        <div className={styles.container}>
          <div className={styles.ctaContent}>
            <h2 className={styles.ctaTitle}>Ready to Start Your Research?</h2>
            <p className={styles.ctaDescription}>
              Partner with ResearchSat to bring your research to space. Our team of experts will guide you through every step of the process.
            </p>
            <div className={styles.ctaButtons}>
              <Link to="/contact" className={styles.ctaButton}>
                Get Started
              </Link>
              <Link to="/missions" className={styles.ctaButtonOutline}>
                View Missions
              </Link>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default MicrogravityGuide;