import React, { useState } from 'react';
import AnalyticsDashboard from '../components/AnalyticsDashboard';
import CookieDemo from '../components/CookieDemo';
import SEO from '../components/SEO';

const TestPage = () => {
  const [activeView, setActiveView] = useState('demo');

  return (
    <>
      <SEO
        title="Cookie Analytics System - ResearchSat"
        description="Interactive demo and analytics dashboard for the advanced cookie-based sales optimization system"
      />
      <div style={{ padding: '20px 0', minHeight: '100vh', background: '#f5f5f5' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 20px' }}>
          <div style={{ textAlign: 'center', marginBottom: '40px' }}>
            <h1 style={{ marginBottom: '15px', color: '#333', fontSize: '2.5rem' }}>
              🚀 ResearchSat Cookie Analytics System
            </h1>
            <p style={{ marginBottom: '30px', color: '#666', fontSize: '1.1rem' }}>
              Advanced cookie-based analytics and lead scoring system for sales optimization
            </p>

            <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginBottom: '20px' }}>
              <button
                onClick={() => setActiveView('demo')}
                style={{
                  padding: '12px 24px',
                  background: activeView === 'demo' ? '#00d4ff' : '#f8f9fa',
                  color: activeView === 'demo' ? 'white' : '#666',
                  border: '1px solid #dee2e6',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
              >
                🍪 Interactive Demo
              </button>
              <button
                onClick={() => setActiveView('dashboard')}
                style={{
                  padding: '12px 24px',
                  background: activeView === 'dashboard' ? '#00d4ff' : '#f8f9fa',
                  color: activeView === 'dashboard' ? 'white' : '#666',
                  border: '1px solid #dee2e6',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontWeight: '500',
                  transition: 'all 0.2s ease'
                }}
              >
                📊 Analytics Dashboard
              </button>
            </div>
          </div>

          {activeView === 'demo' ? <CookieDemo /> : <AnalyticsDashboard />}
        </div>
      </div>
    </>
  );
};

export default TestPage;
