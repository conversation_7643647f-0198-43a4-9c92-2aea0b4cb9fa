/* MicrogravityGuide.module.css - Blog Article Style */

/* General Styles */
.container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 40px;
  width: 100%;
}

/* Article Hero Header */
.articleHero {
  position: relative;
  height: 600px;
  color: #ffffff;
}

.heroImageContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.heroImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.8) 100%);
  z-index: 2;
}

.heroContent {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 80px 0 60px;
  z-index: 3;
}

.backLink {
  display: inline-block;
  color: #e0e0e0;
  text-decoration: none;
  margin-bottom: 30px;
  font-size: 16px;
  transition: color 0.3s ease;
  position: relative;
  padding-left: 20px;
}

.backLink:before {
  content: '←';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.backLink:hover {
  color: #107e7d;
}

.articleMeta {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.articleCategory {
  color: #107e7d;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
}

.articleDate {
  color: #e0e0e0;
  font-size: 14px;
}

.readTime {
  color: #e0e0e0;
  font-size: 14px;
}

.articleTitle {
  color: #ffffff;
  font-size: 48px;
  line-height: 130%;
  font-weight: 500;
  margin-bottom: 20px;
  max-width: 900px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.articleAuthor {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.authorAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  object-fit: contain;
  background: #ffffff;
  padding: 2px;
}

.authorName {
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 500;
}

/* Article Summary Section */
.summarySection {
  background: linear-gradient(
    180deg,
    #107e7d 0%,
    #0d6b6a 100%
  );
  background-image: url('../../assets/images/midsec2.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 40px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  max-height: 288px;
  height: 288px;
  box-sizing: border-box;
}

.summaryContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 1280px;
  position: relative;
}

.summaryContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  flex: 1;
  position: relative;
}

.summaryWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 1280px;
  min-width: 343px;
  position: relative;
}

.summaryInner {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1;
  min-width: 285px;
  position: relative;
  margin-left: 60px;
}

.summaryText {
  color: #fffef6;
  text-align: left;
  font-family: 'Poppins', sans-serif;
  font-size: 23px;
  line-height: 140%;
  font-weight: 500;
  position: relative;
  width: 626px;
  margin: 0;
  max-height: 208px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* Article Content */
.articleContent {
  padding: 80px 0;
  background-color: #121212;
  color: #e0e0e0;
}

.contentWrapper {
  display: flex;
  gap: 60px;
}

.mainContent {
  flex: 0 0 70%;
  font-size: 18px;
  line-height: 1.8;
}

.mainContent h2 {
  color: #107e7d;
  font-size: 32px;
  margin: 40px 0 20px;
  font-weight: 500;
}

.mainContent h3 {
  color: #fffef6;
  font-size: 24px;
  margin: 30px 0 15px;
  font-weight: 500;
}

.mainContent h4 {
  color: #fffef6;
  font-size: 18px;
  margin: 20px 0 10px;
  font-weight: 500;
}

.mainContent p {
  margin-bottom: 20px;
  color: #17242d;
  background: rgba(255, 254, 246, 0.95);
  padding: 15px 20px;
  border-radius: 8px;
  border-left: 4px solid #107e7d;
}

.mainContent ul {
  margin-bottom: 20px;
  padding-left: 20px;
  background: rgba(255, 254, 246, 0.95);
  padding: 15px 20px 15px 40px;
  border-radius: 8px;
  border-left: 4px solid #107e7d;
}

.mainContent li {
  margin-bottom: 10px;
  color: #17242d;
}

/* Stats Grid */
.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin: 25px 0;
}

.statItem {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, rgba(16, 126, 125, 0.2), rgba(0, 0, 0, 0.3));
  border-radius: 12px;
  border: 2px solid rgba(16, 126, 125, 0.4);
  box-shadow: 0 4px 15px rgba(16, 126, 125, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.statItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(16, 126, 125, 0.5);
  border-color: #107e7d;
}

.statNumber {
  font-size: 2rem;
  font-weight: 700;
  color: #107e7d;
  display: block;
  text-shadow: 0 2px 8px rgba(16, 126, 125, 0.4);
}

.statLabel {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8px;
  font-weight: 500;
}

/* Process Steps */
.processStep {
  background: linear-gradient(135deg, rgba(16, 126, 125, 0.15), rgba(0, 0, 0, 0.2));
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  border-left: 4px solid #107e7d;
  box-shadow: 0 4px 15px rgba(16, 126, 125, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.processStep:hover {
  transform: translateX(5px);
  box-shadow: 0 6px 20px rgba(16, 126, 125, 0.4);
  border-left-color: #13a09f;
}

.stepNumber {
  background: linear-gradient(135deg, #107e7d, #0d6b6a);
  color: #ffffff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(16, 126, 125, 0.5);
  border: 2px solid #13a09f;
  flex-shrink: 0;
}

.stepContent h4 {
  margin-top: 0;
  color: #107e7d;
}

.stepContent p {
  margin-bottom: 0;
}

/* Sidebar */
.sidebar {
  flex: 0 0 25%;
}

.sidebarTitle {
  color: #fffef6;
  font-size: 20px;
  margin-bottom: 20px;
  font-weight: 500;
}

.downloadSection {
  margin-bottom: 40px;
  background: linear-gradient(135deg, rgba(16, 126, 125, 0.1), rgba(0, 0, 0, 0.2));
  padding: 25px;
  border-radius: 12px;
  border: 2px solid rgba(16, 126, 125, 0.3);
}

.downloadDescription {
  color: #e0e0e0;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 20px;
}

.downloadButton {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: linear-gradient(135deg, #107e7d 0%, #17242d 100%);
  color: #fffef6;
  padding: 15px 25px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 2px solid #107e7d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Poppins', sans-serif;
  width: 100%;
  justify-content: center;
}

.downloadButton:hover {
  background: linear-gradient(135deg, #13a09f 0%, #1a2b35 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 126, 125, 0.4);
  border-color: #13a09f;
}

.downloadIcon {
  font-size: 18px;
}

.shareSection {
  margin-bottom: 40px;
}

.socialLinks {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.socialLink {
  display: inline-block;
  padding: 8px 16px;
  background-color: #1e1e1e;
  color: #fffef6;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.socialLink:hover {
  background-color: #107e7d;
  transform: translateY(-2px);
}

.tagsSection {
  margin-bottom: 40px;
}

.tagsList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  display: inline-block;
  padding: 6px 12px;
  background-color: #1e1e1e;
  color: #8a8a8a;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.tag:hover {
  background-color: #107e7d;
  color: #ffffff;
}

/* Related Articles */
.relatedArticles {
  padding: 80px 0;
  background-color: #0a0a0a;
}

.relatedTitle {
  color: #fffef6;
  font-size: 36px;
  margin-bottom: 40px;
  text-align: center;
  font-weight: 500;
}

.relatedGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.relatedCard {
  background: linear-gradient(135deg, rgba(16, 126, 125, 0.1), rgba(0, 0, 0, 0.3));
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  text-decoration: none;
  border: 2px solid rgba(16, 126, 125, 0.2);
}

.relatedCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(16, 126, 125, 0.3);
  border-color: #107e7d;
}

.relatedImageContainer {
  height: 200px;
  overflow: hidden;
}

.relatedImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.relatedCard:hover .relatedImage {
  transform: scale(1.05);
}

.relatedContent {
  padding: 20px;
}

.relatedCategory {
  color: #107e7d;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.relatedCardTitle {
  color: #fffef6;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  line-height: 1.4;
}

/* Call to Action */
.ctaSection {
  padding: 80px 0;
  background: linear-gradient(135deg, #17242d 0%, #107e7d 100%);
  text-align: center;
}

.ctaContent {
  max-width: 600px;
  margin: 0 auto;
}

.ctaTitle {
  color: #fffef6;
  font-size: 36px;
  margin-bottom: 20px;
  font-weight: 500;
}

.ctaDescription {
  color: rgba(255, 254, 246, 0.9);
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 40px;
}

.ctaButtons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.ctaButton {
  background: #fffef6;
  color: #17242d;
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid #fffef6;
}

.ctaButton:hover {
  background: transparent;
  color: #fffef6;
  transform: translateY(-2px);
}

.ctaButtonOutline {
  background: transparent;
  color: #fffef6;
  padding: 15px 30px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid #fffef6;
}

.ctaButtonOutline:hover {
  background: #fffef6;
  color: #17242d;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentWrapper {
    flex-direction: column;
    gap: 40px;
  }

  .mainContent {
    flex: none;
  }

  .sidebar {
    flex: none;
  }

  .relatedGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .articleHero {
    height: 400px;
  }

  .articleTitle {
    font-size: 32px;
  }

  .summaryText {
    font-size: 18px;
    width: 100%;
  }

  .summaryInner {
    margin-left: 20px;
  }

  .mainContent {
    font-size: 16px;
  }

  .mainContent h2 {
    font-size: 24px;
  }

  .mainContent h3 {
    font-size: 20px;
  }

  .relatedGrid {
    grid-template-columns: 1fr;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }

  .ctaButton,
  .ctaButtonOutline {
    width: 200px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .articleMeta {
    flex-direction: column;
    gap: 8px;
  }

  .articleTitle {
    font-size: 24px;
  }

  .summarySection {
    height: auto;
    max-height: none;
    padding: 30px 10px;
  }

  .summaryText {
    font-size: 16px;
    -webkit-line-clamp: none;
  }

  .processStep {
    flex-direction: column;
    gap: 15px;
  }

  .stepNumber {
    align-self: flex-start;
  }
}
