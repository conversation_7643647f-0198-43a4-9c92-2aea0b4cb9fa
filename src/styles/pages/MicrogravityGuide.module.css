/* Microgravity Guide - ResearchSat Theme */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Container Styles - Dark Theme with Black & Red */
.guideContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #000000 0%, #1a0000 25%, #330000 50%, #660000 75%, #cc0000 100%);
  background-attachment: fixed;
  font-family: 'Poppins', sans-serif;
  position: relative;
}

/* Download Section at Bottom */
.downloadSection {
  background: linear-gradient(135deg, #000000 0%, #1a0000 50%, #330000 100%);
  padding: 60px 20px;
  border-top: 3px solid #cc0000;
  position: relative;
}

.downloadSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(204, 0, 0, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.downloadContainer {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.downloadTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  text-shadow: 0 0 20px rgba(204, 0, 0, 0.6);
  background: linear-gradient(135deg, #ffffff 0%, #cc0000 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.downloadDescription {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.downloadButton {
  background: linear-gradient(135deg, #cc0000 0%, #660000 50%, #000000 100%);
  color: #ffffff;
  padding: 20px 40px;
  border: 2px solid #cc0000;
  border-radius: 3rem;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  box-shadow: 0 10px 30px rgba(204, 0, 0, 0.4);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: 'Poppins', sans-serif;
  position: relative;
  overflow: hidden;
}

.downloadButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.downloadButton:hover::before {
  left: 100%;
}

.downloadButton:hover {
  background: linear-gradient(135deg, #ff0000 0%, #cc0000 50%, #330000 100%);
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(204, 0, 0, 0.6);
  border-color: #ff0000;
}

.downloadIcon {
  font-size: 1.4rem;
  filter: drop-shadow(0 0 10px rgba(204, 0, 0, 0.8));
}

/* Printable Content */
.printableContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* Page Styles - Dark Theme */
.page {
  width: 100%;
  max-width: 210mm;
  min-height: 297mm;
  padding: 40px;
  margin: 0 auto 40px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(26, 0, 0, 0.9) 50%, rgba(51, 0, 0, 0.85) 100%);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  border: 2px solid rgba(204, 0, 0, 0.3);
  box-shadow: 0 20px 40px rgba(204, 0, 0, 0.4);
  position: relative;
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
}

/* Cover Page - Dark Red Theme */
.coverPage {
  background: linear-gradient(135deg, #000000 0%, #330000 25%, #660000 50%, #cc0000 75%, #ff0000 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  border: 2px solid #cc0000;
  position: relative;
  overflow: hidden;
}

.coverPage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(204, 0, 0, 0.2) 0%, transparent 70%);
  pointer-events: none;
}

.coverLogo {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.95) 100%);
  color: #cc0000;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 3rem;
  margin-bottom: 40px;
  box-shadow: 0 0 40px rgba(204, 0, 0, 0.6);
  border: 3px solid #cc0000;
  position: relative;
  z-index: 2;
}

.coverTitle {
  font-size: 3.2rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 30px;
  line-height: 1.2;
  text-shadow: 0 0 25px rgba(204, 0, 0, 0.8);
  position: relative;
  z-index: 2;
}

.coverSubtitle {
  font-size: 1.6rem;
  margin-bottom: 40px;
  opacity: 0.95;
  font-weight: 500;
  color: #ff6666;
  text-shadow: 0 0 20px rgba(204, 0, 0, 0.8);
  position: relative;
  z-index: 2;
}

.coverDescription {
  font-size: 1.1rem;
  max-width: 600px;
  margin-bottom: 50px;
  opacity: 0.9;
  line-height: 1.6;
  font-weight: 400;
}

.coverFeatures {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
  width: 100%;
  max-width: 700px;
}

.featureItem {
  background: rgba(0, 0, 0, 0.4);
  padding: 25px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(204, 0, 0, 0.4);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.featureItem:hover {
  background: rgba(204, 0, 0, 0.2);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(204, 0, 0, 0.5);
  border-color: #cc0000;
}

.featureIcon {
  font-size: 2.2rem;
  margin-bottom: 15px;
  filter: drop-shadow(0 0 15px rgba(204, 0, 0, 0.8));
}

.featureItem h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: #ffffff;
}

.featureItem p {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

/* Header Styles - Dark Red Theme */
.header {
  border-bottom: 3px solid #cc0000;
  padding-bottom: 20px;
  margin-bottom: 30px;
  background: linear-gradient(135deg, rgba(204, 0, 0, 0.1) 0%, transparent 100%);
}

.logoSection {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.logo {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #cc0000, #660000);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 700;
  font-size: 1.2rem;
  margin-right: 20px;
  box-shadow: 0 4px 15px rgba(204, 0, 0, 0.5);
  border: 2px solid #ff0000;
}

.companyName {
  font-size: 1.8rem;
  font-weight: 700;
  color: #cc0000;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(204, 0, 0, 0.3);
}

.guideTitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* Typography - Dark Red Theme */
.page h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #cc0000;
  margin-bottom: 25px;
  line-height: 1.3;
  text-shadow: 0 2px 8px rgba(204, 0, 0, 0.4);
}

.page h2 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ff6666;
  margin-bottom: 20px;
  margin-top: 30px;
  text-shadow: 0 2px 4px rgba(204, 0, 0, 0.3);
}

.page h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15px;
  margin-top: 20px;
}

.page h4 {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10px;
}

.page p {
  margin-bottom: 15px;
  text-align: justify;
  line-height: 1.6;
}

.page ul, .page ol {
  margin-left: 25px;
  margin-bottom: 20px;
}

.page li {
  margin-bottom: 10px;
  line-height: 1.5;
}

/* Special Elements - Dark Red Theme */
.highlightBox {
  background: linear-gradient(135deg, rgba(204, 0, 0, 0.2), rgba(102, 0, 0, 0.1));
  border-left: 4px solid #cc0000;
  padding: 20px;
  margin: 25px 0;
  border-radius: 0 12px 12px 0;
  box-shadow: 0 4px 15px rgba(204, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.highlightBox h3 {
  color: #ff6666;
  margin-top: 0;
  text-shadow: 0 2px 4px rgba(204, 0, 0, 0.3);
}

.caseStudy {
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(204, 0, 0, 0.4);
  padding: 20px;
  margin: 20px 0;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(204, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.caseStudy h4 {
  color: #cc0000;
  font-weight: 600;
  margin-top: 0;
  text-shadow: 0 2px 4px rgba(204, 0, 0, 0.3);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin: 25px 0;
}

.statItem {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, rgba(204, 0, 0, 0.2), rgba(0, 0, 0, 0.3));
  border-radius: 12px;
  border: 2px solid rgba(204, 0, 0, 0.4);
  box-shadow: 0 4px 15px rgba(204, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.statItem:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(204, 0, 0, 0.5);
  border-color: #cc0000;
}

.statNumber {
  font-size: 2rem;
  font-weight: 700;
  color: #cc0000;
  display: block;
  text-shadow: 0 2px 8px rgba(204, 0, 0, 0.4);
}

.statLabel {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8px;
  font-weight: 500;
}

.twoColumn {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
  margin: 20px 0;
}

.processStep {
  background: linear-gradient(135deg, rgba(204, 0, 0, 0.15), rgba(0, 0, 0, 0.2));
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  border-left: 4px solid #cc0000;
  box-shadow: 0 4px 15px rgba(204, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.processStep:hover {
  transform: translateX(5px);
  box-shadow: 0 6px 20px rgba(204, 0, 0, 0.4);
  border-left-color: #ff0000;
}

.stepNumber {
  background: linear-gradient(135deg, #cc0000, #660000);
  color: #ffffff;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  margin-right: 15px;
  box-shadow: 0 4px 12px rgba(204, 0, 0, 0.5);
  border: 2px solid #ff0000;
}

/* Footer - Dark Red Theme */
.footer {
  position: absolute;
  bottom: 30px;
  left: 40px;
  right: 40px;
  border-top: 2px solid rgba(204, 0, 0, 0.4);
  padding-top: 15px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  background: linear-gradient(135deg, rgba(204, 0, 0, 0.1) 0%, transparent 100%);
}

/* Print Styles - Dark Red Theme */
@media print {
  .guideContainer {
    background: linear-gradient(135deg, #000000 0%, #1a0000 25%, #330000 50%, #660000 75%, #cc0000 100%);
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .downloadSection {
    display: none;
  }

  .page {
    box-shadow: none;
    margin: 0;
    border: 2px solid #cc0000;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95) 0%, rgba(26, 0, 0, 0.9) 50%, rgba(51, 0, 0, 0.85) 100%);
    page-break-after: always;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .page:last-child {
    page-break-after: avoid;
  }

  .coverPage {
    background: linear-gradient(135deg, #000000 0%, #330000 25%, #660000 50%, #cc0000 75%, #ff0000 100%);
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  /* Ensure all colors print correctly */
  * {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .printableContent {
    padding: 20px 10px;
  }
  
  .page {
    padding: 30px 20px;
    margin-bottom: 30px;
  }
  
  .coverTitle {
    font-size: 2.2rem;
  }
  
  .coverFeatures {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .twoColumn {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .downloadButton {
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    font-size: 0.9rem;
  }
}
