/* Analytics Dashboard Styles */
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.header h2 {
  margin: 0;
  color: #333;
  font-size: 2rem;
}

.refreshButton {
  background: #00d4ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.refreshButton:hover {
  background: #00b8e6;
}

.tabs {
  display: flex;
  gap: 0;
  margin-bottom: 30px;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  background: none;
  border: none;
  padding: 15px 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
}

.tab:hover {
  color: #333;
  background: #f5f5f5;
}

.tab.active {
  color: #00d4ff;
  border-bottom-color: #00d4ff;
  background: #f8fcff;
}

.content {
  min-height: 400px;
}

.loading {
  text-align: center;
  padding: 50px;
  color: #666;
  font-size: 18px;
}

/* Overview Tab */
.overview {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.statCard {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.statCard h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.statCard p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.statCard strong {
  color: #333;
}

.qualification {
  font-weight: bold;
  margin-left: 8px;
}

.recentActivity {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recentActivity h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.eventsList {
  max-height: 300px;
  overflow-y: auto;
}

.eventItem {
  display: grid;
  grid-template-columns: 150px 1fr 180px;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.eventItem:last-child {
  border-bottom: none;
}

.eventType {
  font-weight: 500;
  color: #00d4ff;
  text-transform: uppercase;
}

.eventPath {
  color: #666;
  font-family: monospace;
}

.eventTime {
  color: #999;
  text-align: right;
}

/* Lead Scoring Tab */
.leadScoring {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.leadHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.leadHeader h3 {
  margin: 0;
  color: #333;
}

.leadScore {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.scoreValue {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
}

.scoreQualification {
  font-size: 1rem;
  font-weight: bold;
  text-transform: uppercase;
}

.scoringFactors,
.recommendations,
.visitHistory {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scoringFactors h4,
.recommendations h4,
.visitHistory h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.factorItem {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.factorDetails {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.factorDetails span {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-family: monospace;
}

.recommendation {
  display: grid;
  grid-template-columns: 80px 150px 1fr;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;
}

.recommendation:last-child {
  border-bottom: none;
}

.priority {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  text-transform: uppercase;
}

.priority.high {
  background: #ffebee;
  color: #c62828;
}

.priority.medium {
  background: #fff3e0;
  color: #ef6c00;
}

.priority.low {
  background: #e8f5e8;
  color: #2e7d32;
}

.recType {
  font-weight: 500;
  color: #666;
  text-transform: uppercase;
  font-size: 12px;
}

.recMessage {
  color: #333;
  font-size: 14px;
}

.pagesList {
  max-height: 200px;
  overflow-y: auto;
}

.visitItem {
  display: grid;
  grid-template-columns: 1fr 180px;
  gap: 15px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.visitItem:last-child {
  border-bottom: none;
}

.visitPath {
  font-family: monospace;
  color: #666;
}

.visitTime {
  color: #999;
  text-align: right;
}

/* Personalization Tab */
.personalization {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.personalizationHeader {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.personalizationHeader h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.profileInfo {
  display: flex;
  gap: 30px;
}

.profileInfo span {
  color: #666;
}

.personalizedContent {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.contentSection {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.contentSection h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.heroPreview {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.heroPreview h5 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.3rem;
}

.heroPreview p {
  margin: 0 0 20px 0;
  color: #666;
}

.ctaPreview {
  background: #00d4ff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: bold;
}

.servicesList,
.recommendationsList {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.serviceItem,
.recItem {
  display: flex;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  align-items: flex-start;
}

.serviceItem i {
  color: #00d4ff;
  font-size: 1.2rem;
  margin-top: 2px;
}

.serviceItem strong,
.recItem strong {
  display: block;
  color: #333;
  margin-bottom: 5px;
}

.serviceItem p,
.recItem p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.recLink {
  color: #00d4ff;
  text-decoration: none;
  font-weight: 500;
  margin-top: 8px;
  display: inline-block;
}

.recLink:hover {
  text-decoration: underline;
}

/* Cookies Tab */
.cookies {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.consentStatus {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.consentStatus h4 {
  margin: 0 0 15px 0;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.consentGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.consentItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.consentCategory {
  font-weight: 500;
  color: #333;
  text-transform: capitalize;
}

.consentStatus.granted {
  color: #28a745;
  font-weight: bold;
}

.consentStatus.denied {
  color: #dc3545;
  font-weight: bold;
}

.cookiesList {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cookiesList h4 {
  margin: 0 0 20px 0;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.cookieItem {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #00d4ff;
}

.cookieItem:last-child {
  margin-bottom: 0;
}

.cookieHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.cookieHeader strong {
  color: #333;
  font-family: monospace;
}

.cookieCategory {
  background: #e9ecef;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  text-transform: uppercase;
  color: #666;
}

.cookieDescription {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.cookieValue {
  background: #ffffff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  font-family: monospace;
  font-size: 12px;
  color: #333;
  white-space: pre-wrap;
  max-height: 150px;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard {
    padding: 15px;
  }

  .header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .tabs {
    flex-wrap: wrap;
  }

  .tab {
    flex: 1;
    min-width: 120px;
  }

  .statsGrid {
    grid-template-columns: 1fr;
  }

  .eventItem,
  .recommendation,
  .visitItem {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .leadHeader {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .profileInfo {
    flex-direction: column;
    gap: 10px;
  }

  .consentGrid {
    grid-template-columns: 1fr;
  }

  .consentItem {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
