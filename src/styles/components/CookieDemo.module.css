/* <PERSON><PERSON> Component Styles - ResearchSat Theme */
.cookieDemo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Poppins', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #17242D 0%, #0E7369 100%);
  border: 2px solid #0E7369;
  border-radius: 20px;
  color: white;
  box-shadow: 0 20px 40px rgba(14, 115, 105, 0.3);
  backdrop-filter: blur(15px);
}

.header h2 {
  margin: 0 0 15px 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 0 20px rgba(14, 115, 105, 0.6);
}

.header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.95;
  font-weight: 500;
}

.demoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

/* Card Styles */
.statusCard,
.contentCard,
.actionsCard,
.formCard {
  background: rgba(23, 36, 45, 0.9);
  border: 2px solid rgba(14, 115, 105, 0.3);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 20px rgba(14, 115, 105, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.statusCard:hover,
.contentCard:hover,
.actionsCard:hover,
.formCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(14, 115, 105, 0.4);
  border-color: #0E7369;
}

.statusCard h3,
.contentCard h3,
.actionsCard h3,
.formCard h3 {
  margin: 0 0 25px 0;
  color: #0E7369;
  font-size: 1.4rem;
  font-weight: 700;
  border-bottom: 2px solid rgba(14, 115, 105, 0.3);
  padding-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Status Card */
.statusItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid rgba(14, 115, 105, 0.2);
}

.statusItem:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.value {
  font-weight: 700;
  color: #ffffff;
  font-size: 1.2rem;
}

.qualification {
  font-weight: bold;
  font-size: 1.2rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px currentColor;
}

/* Content Card */
.personalizedHero {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border-left: 4px solid #00d4ff;
}

.personalizedHero h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.4rem;
  font-weight: 600;
}

.personalizedHero p {
  margin: 0 0 20px 0;
  color: #666;
  line-height: 1.5;
}

.ctaButton {
  background: #0E7369;
  color: white;
  border: 2px solid #0E7369;
  padding: 15px 30px;
  border-radius: 25px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Poppins', sans-serif;
}

.ctaButton:hover {
  background: transparent;
  color: #0E7369;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(14, 115, 105, 0.4);
}

/* Actions Card */
.actionGroup {
  margin-bottom: 25px;
}

.actionGroup:last-child {
  margin-bottom: 0;
}

.actionGroup h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 500;
}

.buttonGroup {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.actionButton {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 10px 15px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  text-align: left;
}

.actionButton:hover {
  background: #e9ecef;
  border-color: #00d4ff;
  color: #00d4ff;
  transform: translateX(5px);
}

/* Form Card */
.testForm {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.formInput {
  padding: 12px 15px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formInput:focus {
  outline: none;
  border-color: #00d4ff;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.submitButton {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.submitButton:hover {
  background: #218838;
  transform: translateY(-1px);
}

.formNote {
  margin: 15px 0 0 0;
  font-size: 0.85rem;
  color: #666;
  font-style: italic;
  line-height: 1.4;
}

/* Instructions */
.instructions {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.4rem;
  font-weight: 600;
}

.instructions ul {
  margin: 0;
  padding-left: 20px;
  line-height: 1.8;
}

.instructions li {
  margin-bottom: 10px;
  color: #555;
}

.instructions strong {
  color: #333;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cookieDemo {
    padding: 15px;
  }

  .header {
    padding: 20px;
  }

  .header h2 {
    font-size: 1.8rem;
  }

  .header p {
    font-size: 1rem;
  }

  .demoGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .statusCard,
  .contentCard,
  .actionsCard,
  .formCard {
    padding: 20px;
  }

  .buttonGroup {
    gap: 8px;
  }

  .actionButton {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .personalizedHero {
    padding: 15px;
  }

  .personalizedHero h4 {
    font-size: 1.2rem;
  }

  .instructions {
    padding: 20px;
  }

  .instructions ul {
    padding-left: 15px;
  }
}

@media (max-width: 480px) {
  .statusItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .value,
  .qualification {
    font-size: 1rem;
  }

  .ctaButton,
  .submitButton {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

/* Animation for score changes */
@keyframes scoreUpdate {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.value:hover,
.qualification:hover {
  animation: scoreUpdate 0.3s ease;
}
