/* Footer.module.css */
.footer {
  background-color: #000000;
  padding: 2rem 0 1rem;
  color: #ffffff;
  font-family: 'Montserrat', sans-serif;
}

/* Top row with logo */
.footerTop {
  display: flex;
  align-items: center;
  margin-bottom: 2.5rem;
}

.footerLogo {
  max-width: 150px;
  margin-right: auto; /* Push logo to the left */
}

/* Bottom row with columns */
.footerContent {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.footerColumn {
  flex: 1;
  min-width: 180px;
  margin-bottom: 2rem;
  padding-right: 1rem;
}

.footerColumn:last-child {
  flex: 1.2;
}

.footerTitle {
  color: #ff3241;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  font-family: 'Poppins', sans-serif;
}

.footerText {
  color: #ffffff;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  font-weight: 300;
}

.footerLink {
  color: #ffffff;
  text-decoration: none;
  display: block;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  font-weight: 300;
}

.footerLink:hover {
  color: #ff3241;
}

.footerAddress {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.footerContact {
  margin-bottom: 0.5rem;
  font-weight: 300;
  font-size: 0.9rem;
}

.footerDivider {
  width: 100%;
  height: 1px;
  background-color: #202724;
  margin: 1rem 0 2rem;
}

.footerBottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  height: 63px;
  position: relative;
}

.footerCopyright {
  color: #8a8a8a;
  font-size: 16px;
  font-family: 'Inter', sans-serif;
  line-height: 24px;
  font-weight: 400;
}

.socialContainer {
  display: flex;
  align-items: center;
}

.socialLinks {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: 16px;
}

.socialText {
  color: #636363;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;
  line-height: 135%;
  letter-spacing: 0.25px;
  font-weight: 400;
}

.socialIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.socialIcon img {
  height: 30px;
}

.facebookIcon {
  width: 33px;
}

.twitterIcon, .instagramIcon {
  width: 30px;
}

.linkedinIcon {
  width: 32px;
}

.socialIcon:hover {
  transform: translateY(-3px);
}

.emailLabel {
  color: #ccc;
  font-size: 0.9rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-family: 'Poppins', sans-serif;
}

.newsletterForm {
  width: 100%;
  max-width: 350px;
}

.inputContainer {
  display: flex;
  width: 100%;
  position: relative;
}

.newsletterInput {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #333;
  background-color: transparent;
  color: #fff;
  border-radius: 8px 0 0 8px;
  font-family: 'Poppins', sans-serif;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.newsletterInput:focus {
  outline: none;
  border-color: #0E7369;
  box-shadow: 0 0 0 3px rgba(14, 115, 105, 0.2);
  background-color: rgba(14, 115, 105, 0.05);
}

.newsletterInput:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.newsletterInput.inputSuccess {
  border-color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
}

.newsletterInput::placeholder {
  color: #666;
  font-size: 0.9rem;
}

.newsletterButton {
  background-color: #0E7369;
  color: #fff;
  border: 2px solid #0E7369;
  width: 52px;
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.newsletterButton:hover:not(:disabled) {
  background-color: transparent;
  color: #0E7369;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 115, 105, 0.3);
}

.newsletterButton:disabled {
  cursor: not-allowed;
  opacity: 0.8;
}

.newsletterButton.buttonLoading {
  background-color: #0E7369;
  color: #fff;
}

.newsletterButton.buttonSuccess {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.successMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-radius: 6px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.successIcon {
  font-size: 1.2rem;
  filter: drop-shadow(0 0 8px rgba(14, 115, 105, 0.6));
}

.successText {
  color: #28a745;
  font-size: 0.85rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
}

/* Media Queries */
@media (max-width: 991px) {
  .footerColumn {
    min-width: 150px;
  }

  .footerLogo {
    max-width: 120px;
  }
}

@media (max-width: 767px) {
  .footerTop {
    justify-content: center;
    margin-bottom: 2rem;
  }

  .footerLogo {
    margin-right: 0;
  }

  .footerContent {
    flex-direction: column;
  }

  .footerColumn {
    width: 100%;
    margin-bottom: 2rem;
    padding-right: 0;
  }

  .footerBottom {
    flex-direction: column;
    text-align: center;
    height: auto;
    padding-bottom: 20px;
  }

  .footerCopyright {
    margin-bottom: 1.5rem;
  }

  .socialContainer {
    flex-direction: column;
  }

  .socialText {
    margin-bottom: 1rem;
  }

  .socialLinks {
    justify-content: center;
    margin-left: 0;
  }
}

@media (max-width: 576px) {
  /* Add 10vw margin for mobile */
  .footer {
    margin: 10vw;
  }

  /* Ensure logo is centered on mobile */
  .footerTop {
    justify-content: center;
    margin-bottom: 2rem;
  }

  .footerLogo {
    margin-right: 0;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
}
