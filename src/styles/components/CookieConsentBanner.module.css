/* <PERSON><PERSON>sent Banner Styles - ResearchSat Theme */
.cookieBanner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(23, 36, 45, 0.98);
  backdrop-filter: blur(15px);
  border-top: 2px solid #0E7369;
  box-shadow: 0 -4px 20px rgba(14, 115, 105, 0.3);
  z-index: 10000;
  animation: slideUp 0.4s ease-out;
  font-family: 'Poppins', sans-serif;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.bannerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Simple View Styles */
.simpleView {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
}

.bannerText {
  flex: 1;
  color: white;
}

.bannerTitle {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  color: #ffffff;
  font-family: 'Poppins', sans-serif;
}

.bannerDescription {
  font-size: 1rem;
  line-height: 1.5;
  margin: 0 0 10px 0;
  color: rgba(255, 255, 255, 0.9);
  font-family: 'Poppins', sans-serif;
}

.bannerSubtext {
  font-size: 0.85rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Poppins', sans-serif;
}

.policyLink {
  color: #0E7369;
  text-decoration: none;
  margin: 0 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.policyLink:hover {
  color: #ffffff;
  text-decoration: underline;
  text-shadow: 0 0 8px rgba(14, 115, 105, 0.6);
}

.bannerActions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.customizeButton,
.essentialButton,
.acceptButton {
  padding: 12px 24px;
  border: 2px solid;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-family: 'Poppins', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.customizeButton {
  background: transparent;
  color: #0E7369;
  border-color: #0E7369;
}

.customizeButton:hover {
  background: #0E7369;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(14, 115, 105, 0.4);
  transform: translateY(-2px);
}

.essentialButton {
  background: transparent;
  color: #626262;
  border-color: #626262;
}

.essentialButton:hover {
  background: #626262;
  color: #ffffff;
  transform: translateY(-2px);
}

.acceptButton {
  background: #0E7369;
  color: #ffffff;
  border-color: #0E7369;
}

.acceptButton:hover {
  background: transparent;
  color: #0E7369;
  box-shadow: 0 4px 15px rgba(14, 115, 105, 0.4);
  transform: translateY(-2px);
}

/* Details View Styles */
.detailsView {
  max-height: 70vh;
  overflow-y: auto;
}

.detailsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(14, 115, 105, 0.3);
}

.detailsTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin: 0;
  font-family: 'Poppins', sans-serif;
}

.backButton {
  background: transparent;
  border: 2px solid #0E7369;
  color: #0E7369;
  padding: 10px 20px;
  border-radius: 2rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.backButton:hover {
  background: #0E7369;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(14, 115, 105, 0.4);
}

.cookieCategories {
  margin-bottom: 20px;
}

.categorySection {
  margin-bottom: 25px;
  border: 2px solid rgba(14, 115, 105, 0.2);
  border-radius: 12px;
  overflow: hidden;
  background: rgba(23, 36, 45, 0.6);
  backdrop-filter: blur(10px);
}

.categoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 25px;
  background: rgba(14, 115, 105, 0.1);
  gap: 20px;
  border-bottom: 1px solid rgba(14, 115, 105, 0.2);
}

.categoryInfo {
  flex: 1;
}

.categoryTitle {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin: 0 0 5px 0;
}

.categoryDescription {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

.toggleContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.toggle {
  width: 18px;
  height: 18px;
  accent-color: #00d4ff;
  cursor: pointer;
}

.toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.toggleLabel {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
}

.cookieList {
  padding: 0 20px 15px 20px;
}

.cookieItem {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.cookieItem:last-child {
  border-bottom: none;
}

.cookieName {
  font-size: 0.85rem;
  font-weight: 500;
  color: #00d4ff;
  font-family: 'Courier New', monospace;
}

.cookieDescription {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.3;
}

.detailsActions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.saveButton,
.acceptAllButton {
  padding: 14px 28px;
  border: 2px solid;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.saveButton {
  background: transparent;
  color: #626262;
  border-color: #626262;
}

.saveButton:hover {
  background: #626262;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(98, 98, 98, 0.4);
}

.acceptAllButton {
  background: #0E7369;
  color: #ffffff;
  border-color: #0E7369;
}

.acceptAllButton:hover {
  background: transparent;
  color: #0E7369;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(14, 115, 105, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .simpleView {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .bannerActions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .detailsHeader {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .categoryHeader {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .detailsActions {
    flex-direction: column;
  }

  .cookieBanner {
    position: fixed;
    top: 0;
    bottom: auto;
    max-height: 100vh;
    overflow-y: auto;
  }

  .detailsView {
    max-height: none;
  }
}

@media (max-width: 480px) {
  .bannerContent {
    padding: 15px;
  }

  .bannerTitle {
    font-size: 1.1rem;
  }

  .bannerDescription {
    font-size: 0.85rem;
  }

  .customizeButton,
  .essentialButton,
  .acceptButton,
  .saveButton,
  .acceptAllButton {
    padding: 8px 16px;
    font-size: 0.85rem;
  }
}
