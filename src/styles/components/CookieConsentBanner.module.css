/* <PERSON><PERSON>sent Banner Styles */
.cookieBanner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10000;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.bannerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Simple View Styles */
.simpleView {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
}

.bannerText {
  flex: 1;
  color: white;
}

.bannerTitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #ffffff;
}

.bannerDescription {
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 8px 0;
  color: rgba(255, 255, 255, 0.8);
}

.bannerSubtext {
  font-size: 0.8rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
}

.policyLink {
  color: #00d4ff;
  text-decoration: none;
  margin: 0 8px;
  transition: color 0.2s ease;
}

.policyLink:hover {
  color: #ffffff;
  text-decoration: underline;
}

.bannerActions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.customizeButton,
.essentialButton,
.acceptButton {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.customizeButton {
  background: transparent;
  color: #00d4ff;
  border: 1px solid #00d4ff;
}

.customizeButton:hover {
  background: #00d4ff;
  color: #000;
}

.essentialButton {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.essentialButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.acceptButton {
  background: #00d4ff;
  color: #000;
}

.acceptButton:hover {
  background: #00b8e6;
  transform: translateY(-1px);
}

/* Details View Styles */
.detailsView {
  max-height: 70vh;
  overflow-y: auto;
}

.detailsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detailsTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

.backButton {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

.cookieCategories {
  margin-bottom: 20px;
}

.categorySection {
  margin-bottom: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.categoryHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.05);
  gap: 20px;
}

.categoryInfo {
  flex: 1;
}

.categoryTitle {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin: 0 0 5px 0;
}

.categoryDescription {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

.toggleContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.toggle {
  width: 18px;
  height: 18px;
  accent-color: #00d4ff;
  cursor: pointer;
}

.toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.toggleLabel {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  white-space: nowrap;
}

.cookieList {
  padding: 0 20px 15px 20px;
}

.cookieItem {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.cookieItem:last-child {
  border-bottom: none;
}

.cookieName {
  font-size: 0.85rem;
  font-weight: 500;
  color: #00d4ff;
  font-family: 'Courier New', monospace;
}

.cookieDescription {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.3;
}

.detailsActions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.saveButton,
.acceptAllButton {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.saveButton {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.saveButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.acceptAllButton {
  background: #00d4ff;
  color: #000;
}

.acceptAllButton:hover {
  background: #00b8e6;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .simpleView {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .bannerActions {
    flex-wrap: wrap;
    justify-content: center;
  }

  .detailsHeader {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .categoryHeader {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .detailsActions {
    flex-direction: column;
  }

  .cookieBanner {
    position: fixed;
    top: 0;
    bottom: auto;
    max-height: 100vh;
    overflow-y: auto;
  }

  .detailsView {
    max-height: none;
  }
}

@media (max-width: 480px) {
  .bannerContent {
    padding: 15px;
  }

  .bannerTitle {
    font-size: 1.1rem;
  }

  .bannerDescription {
    font-size: 0.85rem;
  }

  .customizeButton,
  .essentialButton,
  .acceptButton,
  .saveButton,
  .acceptAllButton {
    padding: 8px 16px;
    font-size: 0.85rem;
  }
}
