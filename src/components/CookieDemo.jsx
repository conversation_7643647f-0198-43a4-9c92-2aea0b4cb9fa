import React, { useState, useEffect } from 'react';
import styles from '../styles/components/CookieDemo.module.css';
import cookieManager from '../utils/cookieManager';
import analyticsTracker from '../utils/analyticsTracker';
import leadScoringEngine from '../utils/leadScoring';
import personalizationEngine from '../utils/personalizationEngine';

const CookieDemo = () => {
  const [demoData, setDemoData] = useState({
    leadScore: 0,
    qualification: 'COLD',
    userSegment: 'DEFAULT',
    engagementLevel: 'LOW',
    sessionEvents: 0
  });

  const [testFormData, setTestFormData] = useState({
    name: '',
    email: '',
    company: ''
  });

  useEffect(() => {
    updateDemoData();
  }, []);

  const updateDemoData = () => {
    const leadSummary = leadScoringEngine.getLeadSummary();
    const personalization = personalizationEngine.getPersonalizationData();
    const sessionData = cookieManager.getCookie('SESSION_ID');

    setDemoData({
      leadScore: leadSummary.score || 0,
      qualification: leadSummary.qualification || 'COLD',
      userSegment: personalization.userSegment || 'DEFAULT',
      engagementLevel: personalization.engagementLevel || 'LOW',
      sessionEvents: sessionData?.events?.length || 0
    });
  };

  const simulatePageVisit = (page) => {
    analyticsTracker.trackPageView(page, { simulated: true });
    leadScoringEngine.calculateLeadScore();
    setTimeout(updateDemoData, 100);
  };

  const simulateFormSubmission = () => {
    if (!testFormData.email) {
      alert('Please enter at least an email address');
      return;
    }

    analyticsTracker.trackFormSubmission('demo-form', testFormData);
    leadScoringEngine.updateCompanyProfile(testFormData);
    personalizationEngine.updatePersonalization('form_submission', { formData: testFormData });
    
    setTimeout(updateDemoData, 100);
    
    // Reset form
    setTestFormData({ name: '', email: '', company: '' });
  };

  const simulateCalBooking = () => {
    analyticsTracker.trackCalBooking('demo-cal', 'completed');
    leadScoringEngine.calculateLeadScore();
    setTimeout(updateDemoData, 100);
  };

  const simulateScrollDepth = () => {
    analyticsTracker.trackEvent('scroll_depth', { depth: 90, path: '/demo' });
    leadScoringEngine.calculateLeadScore();
    setTimeout(updateDemoData, 100);
  };

  const getQualificationColor = (qualification) => {
    const colors = {
      'HOT': '#ff4444',
      'WARM': '#ff8800',
      'QUALIFIED': '#ffaa00',
      'INTERESTED': '#00aa00',
      'COLD': '#666666'
    };
    return colors[qualification] || '#666666';
  };

  const getPersonalizedContent = () => {
    const personalization = personalizationEngine.getPersonalizationData();
    return personalization.hero || {
      title: "Pioneering Space-Based Research",
      subtitle: "Transform your research with the unique advantages of microgravity",
      cta: "Discover Possibilities"
    };
  };

  const personalizedContent = getPersonalizedContent();

  return (
    <div className={styles.cookieDemo}>
      <div className={styles.header}>
        <h2>🍪 Cookie System Demo</h2>
        <p>Interact with the demo to see how cookies drive personalization and lead scoring</p>
      </div>

      <div className={styles.demoGrid}>
        {/* Current Status */}
        <div className={styles.statusCard}>
          <h3>Current Status</h3>
          <div className={styles.statusItem}>
            <span className={styles.label}>Lead Score:</span>
            <span className={styles.value}>{demoData.leadScore}</span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.label}>Qualification:</span>
            <span 
              className={styles.qualification}
              style={{ color: getQualificationColor(demoData.qualification) }}
            >
              {demoData.qualification}
            </span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.label}>User Segment:</span>
            <span className={styles.value}>{demoData.userSegment}</span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.label}>Engagement:</span>
            <span className={styles.value}>{demoData.engagementLevel}</span>
          </div>
          <div className={styles.statusItem}>
            <span className={styles.label}>Session Events:</span>
            <span className={styles.value}>{demoData.sessionEvents}</span>
          </div>
        </div>

        {/* Personalized Content */}
        <div className={styles.contentCard}>
          <h3>Personalized Content</h3>
          <div className={styles.personalizedHero}>
            <h4>{personalizedContent.title}</h4>
            <p>{personalizedContent.subtitle}</p>
            <button className={styles.ctaButton}>
              {personalizedContent.cta}
            </button>
          </div>
        </div>

        {/* Simulation Actions */}
        <div className={styles.actionsCard}>
          <h3>Simulate User Actions</h3>
          
          <div className={styles.actionGroup}>
            <h4>Page Visits (+Points)</h4>
            <div className={styles.buttonGroup}>
              <button onClick={() => simulatePageVisit('/payloads')} className={styles.actionButton}>
                Visit Payloads (+20)
              </button>
              <button onClick={() => simulatePageVisit('/contact')} className={styles.actionButton}>
                Visit Contact (+25)
              </button>
              <button onClick={() => simulatePageVisit('/book-mission')} className={styles.actionButton}>
                Visit Book Mission (+30)
              </button>
            </div>
          </div>

          <div className={styles.actionGroup}>
            <h4>High-Value Actions</h4>
            <div className={styles.buttonGroup}>
              <button onClick={simulateCalBooking} className={styles.actionButton}>
                Complete Cal Booking (+100)
              </button>
              <button onClick={simulateScrollDepth} className={styles.actionButton}>
                Deep Scroll (+10)
              </button>
            </div>
          </div>
        </div>

        {/* Test Form */}
        <div className={styles.formCard}>
          <h3>Test Form Submission</h3>
          <div className={styles.testForm}>
            <input
              type="text"
              placeholder="Name"
              value={testFormData.name}
              onChange={(e) => setTestFormData({...testFormData, name: e.target.value})}
              className={styles.formInput}
            />
            <input
              type="email"
              placeholder="Email (required)"
              value={testFormData.email}
              onChange={(e) => setTestFormData({...testFormData, email: e.target.value})}
              className={styles.formInput}
            />
            <input
              type="text"
              placeholder="Company"
              value={testFormData.company}
              onChange={(e) => setTestFormData({...testFormData, company: e.target.value})}
              className={styles.formInput}
            />
            <button onClick={simulateFormSubmission} className={styles.submitButton}>
              Submit Form (+50 points)
            </button>
          </div>
          <p className={styles.formNote}>
            Try different email domains (e.g., @nasa.gov, @pfizer.com, @university.edu) 
            to see industry-based personalization
          </p>
        </div>
      </div>

      <div className={styles.instructions}>
        <h3>How It Works</h3>
        <ul>
          <li><strong>Page Visits:</strong> Different pages award different points based on sales value</li>
          <li><strong>Form Submissions:</strong> High-value action that updates company profile and lead scoring</li>
          <li><strong>Email Domains:</strong> Automatically detect industry and company size for personalization</li>
          <li><strong>Engagement Actions:</strong> Cal bookings, deep scrolling, and time on site boost scores</li>
          <li><strong>Personalization:</strong> Content changes based on detected user segment and engagement level</li>
        </ul>
      </div>
    </div>
  );
};

export default CookieDemo;
