import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import styles from '../styles/components/Footer.module.css';
import logoImage from '../assets/images/ResearchSatLogo.png';
import facebookIcon from '../assets/images/social-new/facebook.svg';
import twitterIcon from '../assets/images/social-new/twitter.svg';
import instagramIcon from '../assets/images/social-new/instagram.svg';
import linkedinIcon from '../assets/images/social-new/linkedin.svg';
import analyticsTracker from '../utils/analyticsTracker';
import leadScoringEngine from '../utils/leadScoring';
import personalizationEngine from '../utils/personalizationEngine';
import useAppStore from '../store/appStore';

const Footer = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const addNotification = useAppStore((state) => state.addNotification);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Track newsletter signup with analytics
      analyticsTracker.trackEvent('email_signup', {
        email: email,
        source: 'footer_newsletter',
        timestamp: new Date().toISOString()
      });

      // Update company profile for lead scoring
      leadScoringEngine.updateCompanyProfile({ email });

      // Update personalization
      personalizationEngine.updatePersonalization('email_signup', {
        formData: { email },
        source: 'footer_newsletter'
      });

      // Track with Google Analytics
      if (typeof gtag !== 'undefined') {
        gtag('event', 'newsletter_signup', {
          event_category: 'engagement',
          event_label: 'footer_newsletter',
          value: 1
        });
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success notification
      addNotification({
        type: 'success',
        message: '🚀 Welcome aboard! You\'re now subscribed to ResearchSat updates.',
        duration: 5000
      });

      // Show success state
      setShowSuccess(true);

      // Reset form after delay
      setTimeout(() => {
        setEmail('');
        setShowSuccess(false);
      }, 3000);

      console.log('Newsletter subscription successful for:', email);
      console.log('Lead score updated:', leadScoringEngine.getLeadSummary());

    } catch (error) {
      console.error('Newsletter subscription error:', error);
      addNotification({
        type: 'error',
        message: 'Subscription failed. Please try again.',
        duration: 4000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <footer className={styles.footer}>
      <div className="container">
        {/* Top row with logo */}
        <div className={styles.footerTop}>
          <img src={logoImage} alt="ResearchSat Logo" className={styles.footerLogo} />
        </div>

        {/* Bottom row with 4 columns */}
        <div className={styles.footerContent}>
          {/* Column 1: Address and Contact */}
          <div className={styles.footerColumn}>
            <div className={styles.footerAddress}>
              <p className={styles.footerText}>9 Light Square,</p>
              <p className={styles.footerText}>Adelaide, SA 5000</p>
            </div>
            <p className={styles.footerContact}><EMAIL></p>
            <p className={styles.footerContact}>+61 124.459.8900</p>
          </div>

          {/* Column 2: Navigation Links */}
          <div className={styles.footerColumn}>
            <Link to="/" className={styles.footerLink}>Home</Link>
            <Link to="/about" className={styles.footerLink}>About</Link>
            <Link to="/missions" className={styles.footerLink}>Mission</Link>
            <Link to="/terms-conditions" className={styles.footerLink}>Terms & Conditions</Link>
          </div>

          {/* Column 3: More Links */}
          <div className={styles.footerColumn}>
            <Link to="/news" className={styles.footerLink}>News</Link>
            <Link to="/careers" className={styles.footerLink}>Careers</Link>
            <Link to="/contact" className={styles.footerLink}>Contact</Link>
            <Link to="/privacy-policy" className={styles.footerLink}>Privacy Policy</Link>
          </div>

          {/* Column 4: More Links */}
          <div className={styles.footerColumn}>
            <Link to="/payloads" className={styles.footerLink}>Payloads</Link>
            <Link to="/past-missions" className={styles.footerLink}>Past Missions</Link>
            <Link to="/microgravity-guide" className={styles.footerLink}>Research Guide</Link>
            <Link to="/gallery" className={styles.footerLink}>Gallery</Link>
          </div>

          {/* Column 5: Newsletter */}
          <div className={styles.footerColumn}>
            <h3 className={styles.footerTitle}>Subscribe to our newsletter</h3>
            <p className={styles.footerText}>Stay updated on our latest missions &</p>
            <p className={styles.footerText}>advancements</p>

            <p className={styles.emailLabel}>Email ID</p>
            <form onSubmit={handleSubmit} className={styles.newsletterForm}>
              <div className={styles.inputContainer}>
                <input
                  type="email"
                  placeholder="Enter Email ID"
                  className={`${styles.newsletterInput} ${showSuccess ? styles.inputSuccess : ''}`}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isSubmitting || showSuccess}
                  required
                  onFocus={() => {
                    // Track form interaction
                    analyticsTracker.trackFormInteraction('footer-newsletter', 'email', 'focus');
                  }}
                />
                <button
                  type="submit"
                  className={`${styles.newsletterButton} ${isSubmitting ? styles.buttonLoading : ''} ${showSuccess ? styles.buttonSuccess : ''}`}
                  disabled={isSubmitting || showSuccess}
                >
                  {showSuccess ? (
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20 6L9 17L4 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  ) : isSubmitting ? (
                    <div className={styles.spinner}></div>
                  ) : (
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  )}
                </button>
              </div>
              {showSuccess && (
                <div className={styles.successMessage}>
                  <span className={styles.successIcon}>🚀</span>
                  <span className={styles.successText}>Successfully subscribed!</span>
                </div>
              )}
            </form>
          </div>
        </div>

        <div className={styles.footerDivider}></div>

        <div className={styles.footerBottom}>
          <div className={styles.footerCopyright}>
            Copyright © {new Date().getFullYear()} ResearchSat - All rights reserved
          </div>
          <div className={styles.socialContainer}>
            <div className={styles.socialText}>Reach us</div>
            <div className={styles.socialLinks}>
              <a href="https://www.facebook.com/researchsat/" target="_blank" rel="noopener noreferrer" className={styles.socialIcon}>
                <img src={facebookIcon} alt="Facebook" className={styles.facebookIcon} />
              </a>
              <a href="https://twitter.com/researchsat" target="_blank" rel="noopener noreferrer" className={styles.socialIcon}>
                <img src={twitterIcon} alt="Twitter" className={styles.twitterIcon} />
              </a>
              <a href="https://www.instagram.com/researchsat/" target="_blank" rel="noopener noreferrer" className={styles.socialIcon}>
                <img src={instagramIcon} alt="Instagram" className={styles.instagramIcon} />
              </a>
              <a href="https://www.linkedin.com/company/researchsat/" target="_blank" rel="noopener noreferrer" className={styles.socialIcon}>
                <img src={linkedinIcon} alt="LinkedIn" className={styles.linkedinIcon} />
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
