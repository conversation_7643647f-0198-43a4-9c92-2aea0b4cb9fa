import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styles from '../styles/components/CookieConsentBanner.module.css';
import cookieManager, { COOKIE_CATEGORIES, COOKIE_CONFIG } from '../utils/cookieManager.js';
import analyticsTracker from '../utils/analyticsTracker.js';

const CookieConsentBanner = () => {
  const [showBanner, setShowBanner] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState({
    [COOKIE_CATEGORIES.ESSENTIAL]: true,
    [COOKIE_CATEGORIES.ANALYTICS]: false,
    [COOKIE_CATEGORIES.MARKETING]: false,
    [COOKIE_CATEGORIES.PERSONALIZATION]: false
  });

  useEffect(() => {
    // Check if user has already given consent
    const existingConsent = cookieManager.getConsentPreferences();
    if (!existingConsent.timestamp) {
      // No consent given yet, show banner
      setShowBanner(true);
    } else {
      // Load existing preferences
      setPreferences(existingConsent);
    }
  }, []);

  const handleAcceptAll = () => {
    const allConsent = {
      [COOKIE_CATEGORIES.ESSENTIAL]: true,
      [COOKIE_CATEGORIES.ANALYTICS]: true,
      [COOKIE_CATEGORIES.MARKETING]: true,
      [COOKIE_CATEGORIES.PERSONALIZATION]: true
    };
    
    cookieManager.setConsentPreferences(allConsent);
    setShowBanner(false);
    
    // Initialize analytics tracking
    analyticsTracker.initializeTracking();
    
    // Track consent given
    analyticsTracker.trackEvent('consent_given', { type: 'accept_all' });
  };

  const handleAcceptEssential = () => {
    const essentialOnly = {
      [COOKIE_CATEGORIES.ESSENTIAL]: true,
      [COOKIE_CATEGORIES.ANALYTICS]: false,
      [COOKIE_CATEGORIES.MARKETING]: false,
      [COOKIE_CATEGORIES.PERSONALIZATION]: false
    };
    
    cookieManager.setConsentPreferences(essentialOnly);
    setShowBanner(false);
    
    // Track consent given
    analyticsTracker.trackEvent('consent_given', { type: 'essential_only' });
  };

  const handleCustomPreferences = () => {
    cookieManager.setConsentPreferences(preferences);
    setShowBanner(false);
    
    // Initialize analytics if consent given
    if (preferences[COOKIE_CATEGORIES.ANALYTICS]) {
      analyticsTracker.initializeTracking();
    }
    
    // Track consent given
    analyticsTracker.trackEvent('consent_given', { 
      type: 'custom',
      preferences: preferences
    });
  };

  const handlePreferenceChange = (category, value) => {
    setPreferences(prev => ({
      ...prev,
      [category]: value
    }));
  };

  const getCookiesByCategory = (category) => {
    return Object.entries(COOKIE_CONFIG)
      .filter(([key, config]) => config.category === category)
      .map(([key, config]) => config);
  };

  if (!showBanner) return null;

  return (
    <div className={styles.cookieBanner}>
      <div className={styles.bannerContent}>
        {!showDetails ? (
          // Simple banner view
          <div className={styles.simpleView}>
            <div className={styles.bannerText}>
              <h3 className={styles.bannerTitle}>🍪 We use cookies to enhance your experience</h3>
              <p className={styles.bannerDescription}>
                We use cookies to analyze website traffic, personalize content, and provide social media features. 
                By clicking "Accept All", you consent to our use of cookies.
              </p>
              <p className={styles.bannerSubtext}>
                <Link to="/privacy-policy" className={styles.policyLink}>Privacy Policy</Link> | 
                <Link to="/terms-conditions" className={styles.policyLink}>Cookie Policy</Link>
              </p>
            </div>
            
            <div className={styles.bannerActions}>
              <button 
                className={styles.customizeButton}
                onClick={() => setShowDetails(true)}
              >
                Customize
              </button>
              <button 
                className={styles.essentialButton}
                onClick={handleAcceptEssential}
              >
                Essential Only
              </button>
              <button 
                className={styles.acceptButton}
                onClick={handleAcceptAll}
              >
                Accept All
              </button>
            </div>
          </div>
        ) : (
          // Detailed preferences view
          <div className={styles.detailsView}>
            <div className={styles.detailsHeader}>
              <h3 className={styles.detailsTitle}>Cookie Preferences</h3>
              <button 
                className={styles.backButton}
                onClick={() => setShowDetails(false)}
              >
                ← Back
              </button>
            </div>

            <div className={styles.cookieCategories}>
              {/* Essential Cookies */}
              <div className={styles.categorySection}>
                <div className={styles.categoryHeader}>
                  <div className={styles.categoryInfo}>
                    <h4 className={styles.categoryTitle}>Essential Cookies</h4>
                    <p className={styles.categoryDescription}>
                      Required for basic website functionality. Cannot be disabled.
                    </p>
                  </div>
                  <div className={styles.toggleContainer}>
                    <input 
                      type="checkbox" 
                      checked={true} 
                      disabled 
                      className={styles.toggle}
                    />
                    <span className={styles.toggleLabel}>Always Active</span>
                  </div>
                </div>
                <div className={styles.cookieList}>
                  {getCookiesByCategory(COOKIE_CATEGORIES.ESSENTIAL).map(cookie => (
                    <div key={cookie.name} className={styles.cookieItem}>
                      <span className={styles.cookieName}>{cookie.name}</span>
                      <span className={styles.cookieDescription}>{cookie.description}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Analytics Cookies */}
              <div className={styles.categorySection}>
                <div className={styles.categoryHeader}>
                  <div className={styles.categoryInfo}>
                    <h4 className={styles.categoryTitle}>Analytics Cookies</h4>
                    <p className={styles.categoryDescription}>
                      Help us understand how visitors interact with our website by collecting anonymous information.
                    </p>
                  </div>
                  <div className={styles.toggleContainer}>
                    <input 
                      type="checkbox" 
                      checked={preferences[COOKIE_CATEGORIES.ANALYTICS]}
                      onChange={(e) => handlePreferenceChange(COOKIE_CATEGORIES.ANALYTICS, e.target.checked)}
                      className={styles.toggle}
                    />
                  </div>
                </div>
                <div className={styles.cookieList}>
                  {getCookiesByCategory(COOKIE_CATEGORIES.ANALYTICS).map(cookie => (
                    <div key={cookie.name} className={styles.cookieItem}>
                      <span className={styles.cookieName}>{cookie.name}</span>
                      <span className={styles.cookieDescription}>{cookie.description}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Marketing Cookies */}
              <div className={styles.categorySection}>
                <div className={styles.categoryHeader}>
                  <div className={styles.categoryInfo}>
                    <h4 className={styles.categoryTitle}>Marketing Cookies</h4>
                    <p className={styles.categoryDescription}>
                      Used to track visitors across websites to display relevant advertisements and measure campaign effectiveness.
                    </p>
                  </div>
                  <div className={styles.toggleContainer}>
                    <input 
                      type="checkbox" 
                      checked={preferences[COOKIE_CATEGORIES.MARKETING]}
                      onChange={(e) => handlePreferenceChange(COOKIE_CATEGORIES.MARKETING, e.target.checked)}
                      className={styles.toggle}
                    />
                  </div>
                </div>
                <div className={styles.cookieList}>
                  {getCookiesByCategory(COOKIE_CATEGORIES.MARKETING).map(cookie => (
                    <div key={cookie.name} className={styles.cookieItem}>
                      <span className={styles.cookieName}>{cookie.name}</span>
                      <span className={styles.cookieDescription}>{cookie.description}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Personalization Cookies */}
              <div className={styles.categorySection}>
                <div className={styles.categoryHeader}>
                  <div className={styles.categoryInfo}>
                    <h4 className={styles.categoryTitle}>Personalization Cookies</h4>
                    <p className={styles.categoryDescription}>
                      Remember your preferences and provide customized content and experiences.
                    </p>
                  </div>
                  <div className={styles.toggleContainer}>
                    <input 
                      type="checkbox" 
                      checked={preferences[COOKIE_CATEGORIES.PERSONALIZATION]}
                      onChange={(e) => handlePreferenceChange(COOKIE_CATEGORIES.PERSONALIZATION, e.target.checked)}
                      className={styles.toggle}
                    />
                  </div>
                </div>
                <div className={styles.cookieList}>
                  {getCookiesByCategory(COOKIE_CATEGORIES.PERSONALIZATION).map(cookie => (
                    <div key={cookie.name} className={styles.cookieItem}>
                      <span className={styles.cookieName}>{cookie.name}</span>
                      <span className={styles.cookieDescription}>{cookie.description}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className={styles.detailsActions}>
              <button 
                className={styles.saveButton}
                onClick={handleCustomPreferences}
              >
                Save Preferences
              </button>
              <button 
                className={styles.acceptAllButton}
                onClick={handleAcceptAll}
              >
                Accept All
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CookieConsentBanner;
