import React, { useState, useEffect } from 'react';
import styles from '../styles/components/AnalyticsDashboard.module.css';
import cookieManager from '../utils/cookieManager';
import analyticsTracker from '../utils/analyticsTracker';
import leadScoringEngine from '../utils/leadScoring';
import personalizationEngine from '../utils/personalizationEngine';

const AnalyticsDashboard = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [leadData, setLeadData] = useState(null);
  const [personalizationData, setPersonalizationData] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = () => {
    // Get analytics summary
    const analytics = analyticsTracker.getAnalyticsSummary();
    setAnalyticsData(analytics);

    // Get lead scoring data
    const lead = leadScoringEngine.getLeadSummary();
    setLeadData(lead);

    // Get personalization data
    const personalization = personalizationEngine.getPersonalizationData();
    setPersonalizationData(personalization);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const getQualificationColor = (qualification) => {
    const colors = {
      'HOT': '#ff4444',
      'WARM': '#ff8800',
      'QUALIFIED': '#ffaa00',
      'INTERESTED': '#00aa00',
      'COLD': '#666666'
    };
    return colors[qualification] || '#666666';
  };

  if (!analyticsData) {
    return <div className={styles.loading}>Loading analytics data...</div>;
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h2>Analytics Dashboard</h2>
        <button onClick={loadAnalyticsData} className={styles.refreshButton}>
          <i className="fas fa-sync-alt"></i> Refresh
        </button>
      </div>

      <div className={styles.tabs}>
        <button 
          className={`${styles.tab} ${activeTab === 'overview' ? styles.active : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button 
          className={`${styles.tab} ${activeTab === 'lead' ? styles.active : ''}`}
          onClick={() => setActiveTab('lead')}
        >
          Lead Scoring
        </button>
        <button 
          className={`${styles.tab} ${activeTab === 'personalization' ? styles.active : ''}`}
          onClick={() => setActiveTab('personalization')}
        >
          Personalization
        </button>
        <button 
          className={`${styles.tab} ${activeTab === 'cookies' ? styles.active : ''}`}
          onClick={() => setActiveTab('cookies')}
        >
          Cookies
        </button>
      </div>

      <div className={styles.content}>
        {activeTab === 'overview' && (
          <div className={styles.overview}>
            <div className={styles.statsGrid}>
              <div className={styles.statCard}>
                <h3>Session Info</h3>
                <p><strong>Session ID:</strong> {analyticsData.sessionId}</p>
                <p><strong>Page Views:</strong> {analyticsData.sessionData?.pageViews || 0}</p>
                <p><strong>Events:</strong> {analyticsData.sessionData?.events?.length || 0}</p>
                <p><strong>Last Activity:</strong> {formatDate(analyticsData.sessionData?.lastActivity)}</p>
              </div>

              <div className={styles.statCard}>
                <h3>Engagement</h3>
                <p><strong>Score:</strong> {analyticsData.engagementScore}</p>
                <p><strong>Level:</strong> {personalizationData?.engagementLevel}</p>
                <p><strong>User Segment:</strong> {personalizationData?.userSegment}</p>
              </div>

              <div className={styles.statCard}>
                <h3>User Journey</h3>
                <p><strong>Total Visits:</strong> {analyticsData.userJourney?.totalVisits || 0}</p>
                <p><strong>First Visit:</strong> {formatDate(analyticsData.userJourney?.firstVisit)}</p>
                <p><strong>Last Visit:</strong> {formatDate(analyticsData.userJourney?.lastVisit)}</p>
              </div>

              <div className={styles.statCard}>
                <h3>Lead Qualification</h3>
                <p>
                  <strong>Status:</strong> 
                  <span 
                    className={styles.qualification}
                    style={{ color: getQualificationColor(leadData?.qualification) }}
                  >
                    {leadData?.qualification || 'UNKNOWN'}
                  </span>
                </p>
                <p><strong>Score:</strong> {leadData?.score || 0}</p>
                <p><strong>Total Visits:</strong> {leadData?.totalVisits || 0}</p>
              </div>
            </div>

            <div className={styles.recentActivity}>
              <h3>Recent Events</h3>
              <div className={styles.eventsList}>
                {analyticsData.sessionData?.events?.slice(-10).reverse().map((event, index) => (
                  <div key={index} className={styles.eventItem}>
                    <span className={styles.eventType}>{event.type}</span>
                    <span className={styles.eventPath}>{event.path}</span>
                    <span className={styles.eventTime}>{formatDate(event.timestamp)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'lead' && leadData && (
          <div className={styles.leadScoring}>
            <div className={styles.leadHeader}>
              <h3>Lead Scoring Analysis</h3>
              <div className={styles.leadScore}>
                <span className={styles.scoreValue}>{leadData.score}</span>
                <span 
                  className={styles.scoreQualification}
                  style={{ color: getQualificationColor(leadData.qualification) }}
                >
                  {leadData.qualification}
                </span>
              </div>
            </div>

            <div className={styles.scoringFactors}>
              <h4>Scoring Factors</h4>
              {leadData.factors && Object.entries(leadData.factors).map(([factor, data]) => (
                <div key={factor} className={styles.factorItem}>
                  <strong>{factor.replace(/([A-Z])/g, ' $1').toUpperCase()}:</strong>
                  {typeof data === 'object' ? (
                    <div className={styles.factorDetails}>
                      {Object.entries(data).map(([key, value]) => (
                        <span key={key}>{key}: {JSON.stringify(value)}</span>
                      ))}
                    </div>
                  ) : (
                    <span>{JSON.stringify(data)}</span>
                  )}
                </div>
              ))}
            </div>

            <div className={styles.recommendations}>
              <h4>Recommendations</h4>
              {leadData.recommendations?.map((rec, index) => (
                <div key={index} className={styles.recommendation}>
                  <span className={`${styles.priority} ${styles[rec.priority.toLowerCase()]}`}>
                    {rec.priority}
                  </span>
                  <span className={styles.recType}>{rec.type}</span>
                  <span className={styles.recMessage}>{rec.message}</span>
                </div>
              ))}
            </div>

            <div className={styles.visitHistory}>
              <h4>Recent Page Visits</h4>
              <div className={styles.pagesList}>
                {leadData.visitHistory?.map((visit, index) => (
                  <div key={index} className={styles.visitItem}>
                    <span className={styles.visitPath}>{visit.path}</span>
                    <span className={styles.visitTime}>{formatDate(visit.timestamp)}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'personalization' && personalizationData && (
          <div className={styles.personalization}>
            <div className={styles.personalizationHeader}>
              <h3>Personalization Profile</h3>
              <div className={styles.profileInfo}>
                <span><strong>Segment:</strong> {personalizationData.userSegment}</span>
                <span><strong>Engagement:</strong> {personalizationData.engagementLevel}</span>
              </div>
            </div>

            <div className={styles.personalizedContent}>
              <div className={styles.contentSection}>
                <h4>Personalized Hero</h4>
                <div className={styles.heroPreview}>
                  <h5>{personalizationData.hero?.title}</h5>
                  <p>{personalizationData.hero?.subtitle}</p>
                  <button className={styles.ctaPreview}>{personalizationData.hero?.cta}</button>
                </div>
              </div>

              <div className={styles.contentSection}>
                <h4>Recommended Services</h4>
                <div className={styles.servicesList}>
                  {personalizationData.services?.map((service, index) => (
                    <div key={index} className={styles.serviceItem}>
                      <i className={service.icon}></i>
                      <div>
                        <strong>{service.title}</strong>
                        <p>{service.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className={styles.contentSection}>
                <h4>Content Recommendations</h4>
                <div className={styles.recommendationsList}>
                  {personalizationData.recommendations?.map((rec, index) => (
                    <div key={index} className={styles.recItem}>
                      <strong>{rec.title}</strong>
                      <p>{rec.description}</p>
                      <a href={rec.link} className={styles.recLink}>View →</a>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'cookies' && (
          <div className={styles.cookies}>
            <h3>Cookie Management</h3>
            
            <div className={styles.consentStatus}>
              <h4>Consent Status</h4>
              <div className={styles.consentGrid}>
                {Object.entries(analyticsData.consentGiven).map(([category, status]) => (
                  <div key={category} className={styles.consentItem}>
                    <span className={styles.consentCategory}>{category}</span>
                    <span className={`${styles.consentStatus} ${status ? styles.granted : styles.denied}`}>
                      {status ? 'Granted' : 'Denied'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <div className={styles.cookiesList}>
              <h4>Active Cookies</h4>
              {Object.entries(cookieManager.COOKIE_CONFIG || {}).map(([key, config]) => {
                const value = cookieManager.getCookie(key);
                return (
                  <div key={key} className={styles.cookieItem}>
                    <div className={styles.cookieHeader}>
                      <strong>{config.name}</strong>
                      <span className={styles.cookieCategory}>{config.category}</span>
                    </div>
                    <p className={styles.cookieDescription}>{config.description}</p>
                    <div className={styles.cookieValue}>
                      <strong>Value:</strong> {value ? JSON.stringify(value, null, 2) : 'Not set'}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
