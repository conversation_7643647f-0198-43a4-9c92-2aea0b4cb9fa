const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/HomePage-Ds1uLMfR.js","assets/js/vendor-react-BRbkoAi_.js","assets/js/hero-background-iGDmX_zv.js","assets/js/PartnershipsSectionContainer-DcSJnf0f.js","assets/css/PartnershipsSectionContainer-CingN96U.css","assets/js/avatar-square-CmKYi4da.js","assets/js/arrow-icon-BgG77x6T.js","assets/js/BookMission-4xxwTi5F.js","assets/js/CalButton-BFNiOQ6M.js","assets/css/BookMission-iJha7DC2.css","assets/js/SEO-CiRj42Kt.js","assets/js/vendor-utils-DOb1KAbh.js","assets/css/HomePage-CIcoxzfO.css","assets/js/AboutPage-DWSHHSCD.js","assets/js/SectionDivider-DNQsMo-x.js","assets/css/SectionDivider-Ccx4UF4q.css","assets/js/hero-background-D1hGMsA4.js","assets/css/AboutPage-kyOkVN3s.css","assets/js/FeaturesPage-BeBAVLgH.js","assets/css/FeaturesPage-CezYsF5n.css","assets/js/PayloadsPage-BBEoP-IY.js","assets/js/serv1-Bb5bIGGW.js","assets/css/PayloadsPage-DlS7ZIBj.css","assets/js/MissionsPage-C41dqNiZ.js","assets/js/hero-background-DVoA4ozo.js","assets/js/mission_3-B9p-XygU.js","assets/css/mission_3-DOO511LJ.css","assets/css/MissionsPage-DFjEQebr.css","assets/js/PastMissionsPage-mpWB073g.js","assets/css/PastMissionsPage-C7UBcOj1.css","assets/js/CareersPage-DdtxmUAs.js","assets/css/CareersPage-7qrgowqo.css","assets/js/ContactPage-DGlo0kB6.js","assets/css/ContactPage-Aqdm03PN.css","assets/js/PartnershipsPage-CFLIVn2k.js","assets/css/PartnershipsPage-CEYSZc8-.css","assets/js/NewsPage-ClWpG3d_.js","assets/css/NewsPage-B5wIpVel.css","assets/js/NewsArticlePage--wX97tYz.js","assets/css/NewsArticlePage-DXYI5Qdf.css","assets/js/PrivacyPolicyPage-B7XZKw5y.js","assets/js/TermsConditionsPage-BKhpc_t5.js","assets/js/EmailSignPage-QmWCGt5n.js","assets/js/TestPage-CAcUZ-sy.js","assets/js/BookMissionPage-CVDRRER-.js","assets/js/spacexperiment-email-DxDf5037.js","assets/css/BookMissionPage-DhG1_2b6.css","assets/js/SpaceXperimentPage-DziACns4.js","assets/css/SpaceXperimentPage-CTpI4VuR.css","assets/js/OfferingsPage-8Y6ffZ-f.js","assets/css/OfferingsPage-BujR1ZHB.css","assets/js/GalleryPage-Cmi_ndwY.js","assets/css/GalleryPage-CM9d2xGR.css","assets/js/MicrogravityGuide-DRUgQck6.js","assets/css/MicrogravityGuide-zALcfPLq.css","assets/js/NotFoundPage-BF3u9nrX.js","assets/js/sliders-FN4r_CE7.js"])))=>i.map(i=>d[i]);
var e,t,n=Object.defineProperty,i=(e,t,i)=>((e,t,i)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i)(e,"symbol"!=typeof t?t+"":t,i);import{r as s,a as o,g as r,b as a,R as c,L as l,B as d,u,c as h,d as p}from"./vendor-react-BRbkoAi_.js";import{r as m,a as f}from"./vendor-utils-DOb1KAbh.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var g,_,v={exports:{}},b={};var y,w=(_||(_=1,v.exports=function(){if(g)return b;g=1;var e=s(),t=Symbol.for("react.element"),n=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,o=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,r={key:!0,ref:!0,__self:!0,__source:!0};function a(e,n,s){var a,c={},l=null,d=null;for(a in void 0!==s&&(l=""+s),void 0!==n.key&&(l=""+n.key),void 0!==n.ref&&(d=n.ref),n)i.call(n,a)&&!r.hasOwnProperty(a)&&(c[a]=n[a]);if(e&&e.defaultProps)for(a in n=e.defaultProps)void 0===c[a]&&(c[a]=n[a]);return{$$typeof:t,type:e,key:l,ref:d,props:c,_owner:o.current}}return b.Fragment=n,b.jsx=a,b.jsxs=a,b}()),v.exports),E={};const x=r(function(){if(y)return E;y=1;var e=o();return E.createRoot=e.createRoot,E.hydrateRoot=e.hydrateRoot,E}()),C={},S=function(e,t,n){let i=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),s=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));i=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in C)return;C[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const i=document.createElement("link");return i.rel=t?"stylesheet":"modulepreload",t||(i.as="script"),i.crossOrigin="",i.href=e,s&&i.setAttribute("nonce",s),document.head.appendChild(i),t?new Promise(((t,n)=>{i.addEventListener("load",t),i.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function s(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return i.then((t=>{for(const e of t||[])"rejected"===e.status&&s(e.reason);return e().catch(s)}))};var T,A;const O=r(function(){if(A)return T;A=1;var e="undefined"!=typeof Element,t="function"==typeof Map,n="function"==typeof Set,i="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function s(o,r){if(o===r)return!0;if(o&&r&&"object"==typeof o&&"object"==typeof r){if(o.constructor!==r.constructor)return!1;var a,c,l,d;if(Array.isArray(o)){if((a=o.length)!=r.length)return!1;for(c=a;0!==c--;)if(!s(o[c],r[c]))return!1;return!0}if(t&&o instanceof Map&&r instanceof Map){if(o.size!==r.size)return!1;for(d=o.entries();!(c=d.next()).done;)if(!r.has(c.value[0]))return!1;for(d=o.entries();!(c=d.next()).done;)if(!s(c.value[1],r.get(c.value[0])))return!1;return!0}if(n&&o instanceof Set&&r instanceof Set){if(o.size!==r.size)return!1;for(d=o.entries();!(c=d.next()).done;)if(!r.has(c.value[0]))return!1;return!0}if(i&&ArrayBuffer.isView(o)&&ArrayBuffer.isView(r)){if((a=o.length)!=r.length)return!1;for(c=a;0!==c--;)if(o[c]!==r[c])return!1;return!0}if(o.constructor===RegExp)return o.source===r.source&&o.flags===r.flags;if(o.valueOf!==Object.prototype.valueOf&&"function"==typeof o.valueOf&&"function"==typeof r.valueOf)return o.valueOf()===r.valueOf();if(o.toString!==Object.prototype.toString&&"function"==typeof o.toString&&"function"==typeof r.toString)return o.toString()===r.toString();if((a=(l=Object.keys(o)).length)!==Object.keys(r).length)return!1;for(c=a;0!==c--;)if(!Object.prototype.hasOwnProperty.call(r,l[c]))return!1;if(e&&o instanceof Element)return!1;for(c=a;0!==c--;)if(("_owner"!==l[c]&&"__v"!==l[c]&&"__o"!==l[c]||!o.$$typeof)&&!s(o[l[c]],r[l[c]]))return!1;return!0}return o!=o&&r!=r}return T=function(e,t){try{return s(e,t)}catch(n){if((n.message||"").match(/stack|recursion/i))return!1;throw n}}}());var k,j;const N=r(j?k:(j=1,k=function(e,t,n,i,s,o,r,a){if(!e){var c;if(void 0===t)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[n,i,s,o,r,a],d=0;(c=new Error(t.replace(/%s/g,(function(){return l[d++]})))).name="Invariant Violation"}throw c.framesToPop=1,c}}));var L,I;const P=r(I?L:(I=1,L=function(e,t,n,i){var s=n?n.call(i,e,t):void 0;if(void 0!==s)return!!s;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var o=Object.keys(e),r=Object.keys(t);if(o.length!==r.length)return!1;for(var a=Object.prototype.hasOwnProperty.bind(t),c=0;c<o.length;c++){var l=o[c];if(!a(l))return!1;var d=e[l],u=t[l];if(!1===(s=n?n.call(i,d,u,l):void 0)||void 0===s&&d!==u)return!1}return!0}));var D=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(D||{}),M={rel:["amphtml","canonical","alternate"]},R={type:["application/ld+json"]},H={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},$=Object.values(D),F={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},V=Object.entries(F).reduce(((e,[t,n])=>(e[n]=t,e)),{}),B="data-rh",U="defaultTitle",z="defer",G="encodeSpecialCharacters",W="onChangeClientState",q="titleTemplate",Y="prioritizeSeoTags",Z=(e,t)=>{for(let n=e.length-1;n>=0;n-=1){const i=e[n];if(Object.prototype.hasOwnProperty.call(i,t))return i[t]}return null},K=e=>{let t=Z(e,"title");const n=Z(e,q);if(Array.isArray(t)&&(t=t.join("")),n&&t)return n.replace(/%s/g,(()=>t));const i=Z(e,U);return t||i||void 0},J=e=>Z(e,W)||(()=>{}),Q=(e,t)=>t.filter((t=>void 0!==t[e])).map((t=>t[e])).reduce(((e,t)=>({...e,...t})),{}),X=(e,t)=>t.filter((e=>void 0!==e.base)).map((e=>e.base)).reverse().reduce(((t,n)=>{if(!t.length){const i=Object.keys(n);for(let s=0;s<i.length;s+=1){const o=i[s].toLowerCase();if(-1!==e.indexOf(o)&&n[o])return t.concat(n)}}return t}),[]),ee=(e,t,n)=>{const i={};return n.filter((t=>!!Array.isArray(t[e])||(void 0!==t[e]&&(t[e],console&&console.warn),!1))).map((t=>t[e])).reverse().reduce(((e,n)=>{const s={};n.filter((e=>{let n;const o=Object.keys(e);for(let i=0;i<o.length;i+=1){const s=o[i],r=s.toLowerCase();-1===t.indexOf(r)||"rel"===n&&"canonical"===e[n].toLowerCase()||"rel"===r&&"stylesheet"===e[r].toLowerCase()||(n=r),-1===t.indexOf(s)||"innerHTML"!==s&&"cssText"!==s&&"itemprop"!==s||(n=s)}if(!n||!e[n])return!1;const r=e[n].toLowerCase();return i[n]||(i[n]={}),s[n]||(s[n]={}),!i[n][r]&&(s[n][r]=!0,!0)})).reverse().forEach((t=>e.push(t)));const o=Object.keys(s);for(let t=0;t<o.length;t+=1){const e=o[t],n={...i[e],...s[e]};i[e]=n}return e}),[]).reverse()},te=(e,t)=>{if(Array.isArray(e)&&e.length)for(let n=0;n<e.length;n+=1){if(e[n][t])return!0}return!1},ne=e=>Array.isArray(e)?e.join(""):e,ie=(e,t)=>Array.isArray(e)?e.reduce(((e,n)=>(((e,t)=>{const n=Object.keys(e);for(let i=0;i<n.length;i+=1)if(t[n[i]]&&t[n[i]].includes(e[n[i]]))return!0;return!1})(n,t)?e.priority.push(n):e.default.push(n),e)),{priority:[],default:[]}):{default:e,priority:[]},se=(e,t)=>({...e,[t]:void 0}),oe=["noscript","script","style"],re=(e,t=!0)=>!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),ae=e=>Object.keys(e).reduce(((t,n)=>{const i=void 0!==e[n]?`${n}="${e[n]}"`:`${n}`;return t?`${t} ${i}`:i}),""),ce=(e,t={})=>Object.keys(e).reduce(((t,n)=>(t[F[n]||n]=e[n],t)),t),le=(e,t)=>t.map(((t,n)=>{const i={key:n,[B]:!0};return Object.keys(t).forEach((e=>{const n=F[e]||e;if("innerHTML"===n||"cssText"===n){const e=t.innerHTML||t.cssText;i.dangerouslySetInnerHTML={__html:e}}else i[n]=t[e]})),c.createElement(e,i)})),de=(e,t,n=!0)=>{switch(e){case"title":return{toComponent:()=>((e,t,n)=>{const i=ce(n,{key:t,[B]:!0});return[c.createElement("title",i,t)]})(0,t.title,t.titleAttributes),toString:()=>((e,t,n,i)=>{const s=ae(n),o=ne(t);return s?`<${e} ${B}="true" ${s}>${re(o,i)}</${e}>`:`<${e} ${B}="true">${re(o,i)}</${e}>`})(e,t.title,t.titleAttributes,n)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>ce(t),toString:()=>ae(t)};default:return{toComponent:()=>le(e,t),toString:()=>((e,t,n=!0)=>t.reduce(((t,i)=>{const s=i,o=Object.keys(s).filter((e=>!("innerHTML"===e||"cssText"===e))).reduce(((e,t)=>{const i=void 0===s[t]?t:`${t}="${re(s[t],n)}"`;return e?`${e} ${i}`:i}),""),r=s.innerHTML||s.cssText||"",a=-1===oe.indexOf(e);return`${t}<${e} ${B}="true" ${o}${a?"/>":`>${r}</${e}>`}`}),""))(e,t,n)}}},ue=e=>{const{baseTag:t,bodyAttributes:n,encode:i=!0,htmlAttributes:s,noscriptTags:o,styleTags:r,title:a="",titleAttributes:c,prioritizeSeoTags:l}=e;let{linkTags:d,metaTags:u,scriptTags:h}=e,p={toComponent:()=>{},toString:()=>""};return l&&({priorityMethods:p,linkTags:d,metaTags:u,scriptTags:h}=(({metaTags:e,linkTags:t,scriptTags:n,encode:i})=>{const s=ie(e,H),o=ie(t,M),r=ie(n,R);return{priorityMethods:{toComponent:()=>[...le("meta",s.priority),...le("link",o.priority),...le("script",r.priority)],toString:()=>`${de("meta",s.priority,i)} ${de("link",o.priority,i)} ${de("script",r.priority,i)}`},metaTags:s.default,linkTags:o.default,scriptTags:r.default}})(e)),{priority:p,base:de("base",t,i),bodyAttributes:de("bodyAttributes",n,i),htmlAttributes:de("htmlAttributes",s,i),link:de("link",d,i),meta:de("meta",u,i),noscript:de("noscript",o,i),script:de("script",h,i),style:de("style",r,i),title:de("title",{title:a,titleAttributes:c},i)}},he=[],pe=!("undefined"==typeof window||!window.document||!window.document.createElement),me=class{constructor(e,t){i(this,"instances",[]),i(this,"canUseDOM",pe),i(this,"context"),i(this,"value",{setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?he:this.instances,add:e=>{(this.canUseDOM?he:this.instances).push(e)},remove:e=>{const t=(this.canUseDOM?he:this.instances).indexOf(e);(this.canUseDOM?he:this.instances).splice(t,1)}}}),this.context=e,this.canUseDOM=t||!1,t||(e.helmet=ue({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},fe=c.createContext({}),ge=(e=class extends a.Component{constructor(t){super(t),i(this,"helmetData"),this.helmetData=new me(this.props.context||{},e.canUseDOM)}render(){return c.createElement(fe.Provider,{value:this.helmetData.value},this.props.children)}},i(e,"canUseDOM",pe),e),_e=(e,t)=>{const n=document.head||document.querySelector("head"),i=n.querySelectorAll(`${e}[${B}]`),s=[].slice.call(i),o=[];let r;return t&&t.length&&t.forEach((t=>{const n=document.createElement(e);for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))if("innerHTML"===e)n.innerHTML=t.innerHTML;else if("cssText"===e)n.styleSheet?n.styleSheet.cssText=t.cssText:n.appendChild(document.createTextNode(t.cssText));else{const i=e,s=void 0===t[i]?"":t[i];n.setAttribute(e,s)}n.setAttribute(B,"true"),s.some(((e,t)=>(r=t,n.isEqualNode(e))))?s.splice(r,1):o.push(n)})),s.forEach((e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),o.forEach((e=>n.appendChild(e))),{oldTags:s,newTags:o}},ve=(e,t)=>{const n=document.getElementsByTagName(e)[0];if(!n)return;const i=n.getAttribute(B),s=i?i.split(","):[],o=[...s],r=Object.keys(t);for(const a of r){const e=t[a]||"";n.getAttribute(a)!==e&&n.setAttribute(a,e),-1===s.indexOf(a)&&s.push(a);const i=o.indexOf(a);-1!==i&&o.splice(i,1)}for(let a=o.length-1;a>=0;a-=1)n.removeAttribute(o[a]);s.length===o.length?n.removeAttribute(B):n.getAttribute(B)!==r.join(",")&&n.setAttribute(B,r.join(","))},be=(e,t)=>{const{baseTag:n,bodyAttributes:i,htmlAttributes:s,linkTags:o,metaTags:r,noscriptTags:a,onChangeClientState:c,scriptTags:l,styleTags:d,title:u,titleAttributes:h}=e;ve("body",i),ve("html",s),((e,t)=>{void 0!==e&&document.title!==e&&(document.title=ne(e)),ve("title",t)})(u,h);const p={baseTag:_e("base",n),linkTags:_e("link",o),metaTags:_e("meta",r),noscriptTags:_e("noscript",a),scriptTags:_e("script",l),styleTags:_e("style",d)},m={},f={};Object.keys(p).forEach((e=>{const{newTags:t,oldTags:n}=p[e];t.length&&(m[e]=t),n.length&&(f[e]=p[e].oldTags)})),t&&t(),c(e,m,f)},ye=null,we=e=>{ye&&cancelAnimationFrame(ye),e.defer?ye=requestAnimationFrame((()=>{be(e,(()=>{ye=null}))})):(be(e),ye=null)},Ee=class extends a.Component{constructor(){super(...arguments),i(this,"rendered",!1)}shouldComponentUpdate(e){return!P(e,this.props)}componentDidUpdate(){this.emitChange()}componentWillUnmount(){const{helmetInstances:e}=this.props.context;e.remove(this),this.emitChange()}emitChange(){const{helmetInstances:e,setHelmet:t}=this.props.context;let n=null;const i=(s=e.get().map((e=>{const t={...e.props};return delete t.context,t})),{baseTag:X(["href"],s),bodyAttributes:Q("bodyAttributes",s),defer:Z(s,z),encode:Z(s,G),htmlAttributes:Q("htmlAttributes",s),linkTags:ee("link",["rel","href"],s),metaTags:ee("meta",["name","charset","http-equiv","property","itemprop"],s),noscriptTags:ee("noscript",["innerHTML"],s),onChangeClientState:J(s),scriptTags:ee("script",["src","innerHTML"],s),styleTags:ee("style",["cssText"],s),title:K(s),titleAttributes:Q("titleAttributes",s),prioritizeSeoTags:te(s,Y)});var s;ge.canUseDOM?we(i):ue&&(n=ue(i)),t(n)}init(){if(this.rendered)return;this.rendered=!0;const{helmetInstances:e}=this.props.context;e.add(this),this.emitChange()}render(){return this.init(),null}},xe=(t=class extends a.Component{shouldComponentUpdate(e){return!O(se(this.props,"helmetData"),se(e,"helmetData"))}mapNestedChildrenToProps(e,t){if(!t)return null;switch(e.type){case"script":case"noscript":return{innerHTML:t};case"style":return{cssText:t};default:throw new Error(`<${e.type} /> elements are self-closing and can not contain children. Refer to our API for more information.`)}}flattenArrayTypeChildren(e,t,n,i){return{...t,[e.type]:[...t[e.type]||[],{...n,...this.mapNestedChildrenToProps(e,i)}]}}mapObjectTypeChildren(e,t,n,i){switch(e.type){case"title":return{...t,[e.type]:i,titleAttributes:{...n}};case"body":return{...t,bodyAttributes:{...n}};case"html":return{...t,htmlAttributes:{...n}};default:return{...t,[e.type]:{...n}}}}mapArrayTypeChildrenToProps(e,t){let n={...t};return Object.keys(e).forEach((t=>{n={...n,[t]:e[t]}})),n}warnOnInvalidChildren(e,t){return N($.some((t=>e.type===t)),"function"==typeof e.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":`Only elements types ${$.join(", ")} are allowed. Helmet does not support rendering <${e.type}> elements. Refer to our API for more information.`),N(!t||"string"==typeof t||Array.isArray(t)&&!t.some((e=>"string"!=typeof e)),`Helmet expects a string as a child of <${e.type}>. Did you forget to wrap your children in braces? ( <${e.type}>{\`\`}</${e.type}> ) Refer to our API for more information.`),!0}mapChildrenToProps(e,t){let n={};return c.Children.forEach(e,(e=>{if(!e||!e.props)return;const{children:i,...s}=e.props,o=Object.keys(s).reduce(((e,t)=>(e[V[t]||t]=s[t],e)),{});let{type:r}=e;switch("symbol"==typeof r?r=r.toString():this.warnOnInvalidChildren(e,i),r){case"Symbol(react.fragment)":t=this.mapChildrenToProps(i,t);break;case"link":case"meta":case"noscript":case"script":case"style":n=this.flattenArrayTypeChildren(e,n,o,i);break;default:t=this.mapObjectTypeChildren(e,t,o,i)}})),this.mapArrayTypeChildrenToProps(n,t)}render(){const{children:e,...t}=this.props;let n={...t},{helmetData:i}=t;if(e&&(n=this.mapChildrenToProps(e,n)),i&&!(i instanceof me)){i=new me(i.context,!0),delete n.helmetData}return i?c.createElement(Ee,{...n,context:i.value}):c.createElement(fe.Consumer,null,(e=>c.createElement(Ee,{...n,context:e})))}},i(t,"defaultProps",{defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1}),t);const Ce={nav:"_nav_12ucu_2",frame1000006514:"_frame1000006514_12ucu_26",logoContainer:"_logoContainer_12ucu_36",logoImage:"_logoImage_12ucu_49",navLinksContainer:"_navLinksContainer_12ucu_58",navLinksGroup:"_navLinksGroup_12ucu_69",navLinkHome:"_navLinkHome_12ucu_87",navLinkHomeText:"_navLinkHomeText_12ucu_98",navLinkServices:"_navLinkServices_12ucu_112",navLinkServicesText:"_navLinkServicesText_12ucu_123",menuContainer:"_menuContainer_12ucu_137",menuButton:"_menuButton_12ucu_158",menuIcon:"_menuIcon_12ucu_171",menuText:"_menuText_12ucu_179",contactButton:"_contactButton_12ucu_193",contactText:"_contactText_12ucu_214",dropdownMenu:"_dropdownMenu_12ucu_229",dropdownColumns:"_dropdownColumns_12ucu_263",dropdownColumn:"_dropdownColumn_12ucu_263",mobileToggler:"_mobileToggler_12ucu_308",mobileMenu:"_mobileMenu_12ucu_318",mobileMenuList:"_mobileMenuList_12ucu_324",topNavCollapse:"_topNavCollapse_12ucu_354",navHidden:"_navHidden_12ucu_359"},Se="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M2%202H7V7H2V2ZM9%202H14V7H9V2ZM2%209H7V14H2V9ZM9%209H14V14H9V9Z'%20fill='%23FF3141'/%3e%3c/svg%3e",Te=()=>{const[e,t]=a.useState(!1),[n,i]=a.useState(!1),[s,o]=a.useState(!0),[r,c]=a.useState(0),[d,u]=a.useState(!1),h=a.useRef(null);a.useEffect((()=>{const e=()=>{const e=window.scrollY;t(e>60),e<60?o(!0):e>r+10?o(!1):r>e+10&&o(!0),setTimeout((()=>{c(e)}),50)};return window.addEventListener("scroll",e,{passive:!0}),()=>{window.removeEventListener("scroll",e)}}),[r]),a.useEffect((()=>{const e=()=>{u(window.innerWidth<=768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)}),[]),a.useEffect((()=>{const e=e=>{h.current&&!h.current.contains(e.target)&&i(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[]);return w.jsxs("nav",{className:`${Ce.nav} ${e?Ce.topNavCollapse:""} ${s?"":Ce.navHidden}`,children:[w.jsxs("div",{className:Ce.frame1000006514,children:[w.jsx(l,{className:Ce.logoContainer,to:"/",children:w.jsx("img",{className:Ce.logoImage,src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20width='100%25'%20height='100%25'%20viewBox='0%200%20291%20260'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20xml:space='preserve'%20xmlns:serif='http://www.serif.com/'%20style='fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;'%3e%3cg%20transform='matrix(1,0,0,1,-50.9992,-0.603858)'%3e%3cg%20id='Untitled'%3e%3cg%3e%3cpath%20d='M223.68,217.052L198.724,167.606C188.312,171.895%20178.66,175.441%20169.397,179.794C164.83,181.944%20161.04,182.446%20157.827,181.648L151.954,189.417C172.383,201.831%20197.041,207.296%20223.68,217.052Z'%20style='fill:rgb(103,125,124);fill-rule:nonzero;'/%3e%3cpath%20d='M146.846,170.464C134.537,143.926%20141.623,118.23%20166.4,102.449C155.3,80.389%20144.853,59.005%20133.739,37.986C131.44,33.633%20127.29,28.99%20122.897,27.249C100.155,18.253%2077.047,10.145%2050.999,0.604C55.642,9.487%2058.93,15.627%2062.07,21.843C83.715,64.719%20105.065,107.74%20127.102,150.421C134.249,164.263%20140.108,182.226%20151.954,189.426L157.827,181.657C153.091,180.47%20149.618,176.442%20146.846,170.464Z'%20style='fill:rgb(255,50,65);fill-rule:nonzero;'/%3e%3cpath%20d='M330.796,239.347C309.151,196.471%20287.801,153.451%20265.764,110.769C258.625,96.93%20252.766,78.967%20240.921,71.768L235.047,79.536C239.783,80.714%20243.257,84.759%20246.028,90.72C258.338,117.258%20251.252,142.954%20226.475,158.735C237.575,180.795%20248.022,202.179%20259.136,223.198C261.434,227.551%20265.584,232.194%20269.978,233.935C292.72,242.931%20315.828,251.039%20341.875,260.58C337.241,251.706%20333.944,245.566%20330.796,239.347Z'%20style='fill:rgb(103,125,124);fill-rule:nonzero;'/%3e%3cpath%20d='M169.194,44.133L194.151,93.578C204.563,89.289%20214.214,85.743%20223.477,81.39C228.045,79.24%20231.835,78.738%20235.047,79.536L240.921,71.768C220.491,59.353%20195.834,53.889%20169.194,44.133Z'%20style='fill:rgb(255,50,65);fill-rule:nonzero;'/%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e",alt:"ResearchSat Logo"})}),w.jsxs("div",{className:Ce.navLinksContainer,children:[w.jsxs("div",{className:Ce.navLinksGroup,children:[w.jsx(l,{className:Ce.navLinkHome,to:"/",onClick:()=>window.scrollTo(0,0),children:w.jsx("span",{className:Ce.navLinkHomeText,children:"Home"})}),w.jsx(l,{className:Ce.navLinkServices,to:"/about",children:w.jsx("span",{className:Ce.navLinkServicesText,children:"About"})})]}),w.jsxs("div",{className:Ce.menuContainer,onClick:()=>{i(!n)},ref:h,children:[w.jsxs("button",{className:Ce.menuButton,type:"button",children:[w.jsx("img",{className:Ce.menuIcon,src:"data:image/svg+xml,%3csvg%20width='25'%20height='24'%20viewBox='0%200%2025%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cmask%20id='mask0_180_31503'%20style='mask-type:alpha'%20maskUnits='userSpaceOnUse'%20x='0'%20y='0'%20width='25'%20height='24'%3e%3crect%20x='0.5'%20width='24'%20height='24'%20fill='%23D9D9D9'/%3e%3c/mask%3e%3cg%20mask='url(%23mask0_180_31503)'%3e%3cpath%20d='M11.05%2018.2L16.225%2012H12.225L12.95%206.325L8.325%2013H11.8L11.05%2018.2ZM8.5%2022L9.5%2015H4.5L13.5%202H15.5L14.5%2010H20.5L10.5%2022H8.5Z'%20fill='%23FF3241'/%3e%3c/g%3e%3c/svg%3e",alt:"Menu"}),w.jsx("span",{className:Ce.menuText,children:"Menu"})]}),n&&w.jsx("div",{className:Ce.dropdownMenu,children:w.jsxs("div",{className:Ce.dropdownColumns,children:[w.jsxs("div",{className:Ce.dropdownColumn,children:[w.jsxs(l,{to:"/",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201.5L1%207H3V14H7V10H9V14H13V7H15L8%201.5Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Home",width:"16",height:"16"}),"Home"]}),w.jsxs(l,{to:"/about",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201C4.13401%201%201%204.13401%201%208C1%2011.866%204.13401%2015%208%2015C11.866%2015%2015%2011.866%2015%208C15%204.13401%2011.866%201%208%201ZM8%206.5C8.55228%206.5%209%206.94772%209%207.5V11.5C9%2012.0523%208.55228%2012.5%208%2012.5C7.44772%2012.5%207%2012.0523%207%2011.5V7.5C7%206.94772%207.44772%206.5%208%206.5ZM8%205.5C7.44772%205.5%207%205.05228%207%204.5C7%203.94772%207.44772%203.5%208%203.5C8.55228%203.5%209%203.94772%209%204.5C9%205.05228%208.55228%205.5%208%205.5Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"About",width:"16",height:"16"}),"About"]}),w.jsxs(l,{to:"/features",onClick:()=>i(!1),children:[w.jsx("img",{src:Se,alt:"Features",width:"16",height:"16"}),"Features"]}),w.jsxs(l,{to:"/offerings",onClick:()=>i(!1),children:[w.jsx("img",{src:Se,alt:"Offerings",width:"16",height:"16"}),"Our Offerings"]})]}),w.jsxs("div",{className:Ce.dropdownColumn,children:[w.jsxs(l,{to:"/missions",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201L9.93%205.2L14.5%205.8L11.25%209.1L12%2014L8%2011.7L4%2014L4.75%209.1L1.5%205.8L6.07%205.2L8%201Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Mission",width:"16",height:"16"}),"Mission"]}),w.jsxs(l,{to:"/payloads",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%201L1%204.5V7.5L8%2011L14%207.5V10.5H15V4.5L8%201ZM8%202.25L13%204.75L8%207.25L3%204.75L8%202.25ZM3%209.5V6L7.5%208.25V11.75L3%209.5ZM13%2012.5V14.5H3V12.5H2V15.5H14V12.5H13Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Payloads",width:"16",height:"16"}),"Payloads"]}),w.jsxs(l,{to:"/gallery",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20width='800px'%20height='800px'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M20.4692%2010.9182C20.4692%2010.9182%2018.6222%2010.3027%2016.1595%207.8401C13.6969%205.37747%2013.0813%203.53027%2013.0813%203.53027'%20stroke='%231C274C'%20stroke-width='1.5'/%3e%3cpath%20d='M20.4698%2010.9178C18.4297%2012.9579%2015.122%2012.9579%2013.0819%2010.9178C11.0418%208.87772%2011.0418%205.57005%2013.0819%203.52995M20.4698%2010.9178C22.5099%208.87772%2022.5099%205.57005%2020.4698%203.52995C19.7634%202.82354%2018.905%202.36174%2017.9999%202.14453M20.4698%2010.9178L17.6464%2016M13.0819%203.52995C13.6429%202.9689%2014.2999%202.56214%2014.9999%202.30968M13.0819%203.52995L6.83594%207M16.1601%207.83977L10.9999%2012.9999'%20stroke='%231C274C'%20stroke-width='1.5'%20stroke-linecap='round'/%3e%3cpath%20d='M2%209.68721L5%208.02051M14.3132%2022.0003L16.5%2018.0005M5%2019.0003L9%2015.0004'%20stroke='%231C274C'%20stroke-width='1.5'%20stroke-linecap='round'/%3e%3c/svg%3e",alt:"Gallery",width:"16",height:"16"}),"Gallery"]})]}),w.jsxs("div",{className:Ce.dropdownColumn,children:[w.jsxs(l,{to:"/news",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M14%202H2V3H14V2ZM14%204H2V5H14V4ZM14%206H2V7H14V6ZM14%208H2V9H14V8ZM11%2010H2V11H11V10ZM11%2012H2V13H11V12Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"News",width:"16",height:"16"}),"News"]}),w.jsxs(l,{to:"/careers",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M6%202V4H10V2H12V4H14V6H12V12H14V14H2V12H4V6H2V4H4V2H6ZM6%206V12H10V6H6Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Careers",width:"16",height:"16"}),"Careers"]}),w.jsxs(l,{to:"/contact",onClick:()=>i(!1),children:[w.jsx("img",{src:"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='16'%20height='16'%20viewBox='0%200%2016%2016'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M8%202C6.34%202%205%203.34%205%205C5%206.66%206.34%208%208%208C9.66%208%2011%206.66%2011%205C11%203.34%209.66%202%208%202ZM8%209.5C5.33%209.5%200%2010.84%200%2013.5V15H16V13.5C16%2010.84%2010.67%209.5%208%209.5Z'%20fill='%23FF3141'/%3e%3c/svg%3e",alt:"Contact",width:"16",height:"16"}),"Contact"]})]})]})})]}),w.jsx(l,{className:Ce.contactButton,to:"/book-mission",children:w.jsx("span",{className:Ce.contactText,children:d?"Book":"Book Mission"})})]}),w.jsx("button",{className:Ce.mobileToggler,type:"button","data-bs-toggle":"collapse","data-bs-target":"#mobileNavMenu","aria-controls":"mobileNavMenu","aria-expanded":"false","aria-label":"Toggle navigation",children:w.jsx("span",{className:Ce.togglerIcon})})]}),w.jsx("div",{className:"collapse navbar-collapse",id:"mobileNavMenu",children:w.jsx("div",{className:Ce.mobileMenu,children:w.jsxs("ul",{className:Ce.mobileMenuList,children:[w.jsx("li",{children:w.jsx(l,{to:"/",onClick:()=>window.scrollTo(0,0),children:"Home"})}),w.jsx("li",{children:w.jsx(l,{to:"/about",children:"About"})}),w.jsx("li",{children:w.jsx(l,{to:"/features",children:"Features"})}),w.jsx("li",{children:w.jsx(l,{to:"/offerings",children:"Our Offerings"})}),w.jsx("li",{children:w.jsx(l,{to:"/missions",children:"Mission"})}),w.jsx("li",{children:w.jsx(l,{to:"/payloads",children:"Payloads"})}),w.jsx("li",{children:w.jsx(l,{to:"/gallery",children:"Gallery"})}),w.jsx("li",{children:w.jsx(l,{to:"/news",children:"News"})}),w.jsx("li",{children:w.jsx(l,{to:"/careers",children:"Careers"})}),w.jsx("li",{children:w.jsx(l,{to:"/contact",children:"Contact"})}),w.jsx("li",{children:w.jsx(l,{to:"/book-mission",children:d?"Book":"Book Mission"})})]})})})]})},Ae="_footer_uwcrv_2",Oe="_footerTop_uwcrv_10",ke="_footerLogo_uwcrv_16",je="_footerContent_uwcrv_22",Ne="_footerColumn_uwcrv_29",Le="_footerTitle_uwcrv_40",Ie="_footerText_uwcrv_48",Pe="_footerLink_uwcrv_55",De="_footerAddress_uwcrv_69",Me="_footerContact_uwcrv_74",Re="_footerDivider_uwcrv_80",He="_footerBottom_uwcrv_87",$e="_footerCopyright_uwcrv_96",Fe="_socialContainer_uwcrv_104",Ve="_socialLinks_uwcrv_109",Be="_socialText_uwcrv_116",Ue="_socialIcon_uwcrv_125",ze="_facebookIcon_uwcrv_136",Ge="_twitterIcon_uwcrv_140",We="_instagramIcon_uwcrv_140",qe="_linkedinIcon_uwcrv_144",Ye="_emailLabel_uwcrv_152",Ze="_newsletterForm_uwcrv_160",Ke="_inputContainer_uwcrv_165",Je="_newsletterInput_uwcrv_171",Qe="_inputSuccess_uwcrv_195",Xe="_newsletterButton_uwcrv_205",et="_buttonLoading_uwcrv_233",tt="_buttonSuccess_uwcrv_238",nt="_spinner_uwcrv_244",it="_successMessage_uwcrv_258",st="_successIcon_uwcrv_281",ot="_successText_uwcrv_286",rt="essential",at="analytics",ct="marketing",lt="personalization",dt={USER_PREFERENCES:{name:"researchsat_preferences",category:rt,expireDays:365,description:"Stores user preferences and settings"},CONSENT_PREFERENCES:{name:"researchsat_consent",category:rt,expireDays:365,description:"Stores cookie consent preferences"},SESSION_ID:{name:"researchsat_session",category:at,expireDays:1,description:"Tracks user session for analytics"},USER_JOURNEY:{name:"researchsat_journey",category:at,expireDays:30,description:"Tracks user journey across visits"},ENGAGEMENT_SCORE:{name:"researchsat_engagement",category:at,expireDays:90,description:"Calculates user engagement score"},LEAD_SOURCE:{name:"researchsat_source",category:ct,expireDays:30,description:"Tracks lead source and campaign data"},RETARGETING:{name:"researchsat_retarget",category:ct,expireDays:90,description:"Enables retargeting campaigns"},INTEREST_PROFILE:{name:"researchsat_interests",category:lt,expireDays:180,description:"Stores user interest profile for personalization"},COMPANY_PROFILE:{name:"researchsat_company",category:lt,expireDays:365,description:"Stores company information for B2B targeting"}};const ut=new class{constructor(){try{this.consentGiven=this.getConsentPreferences(),this.sessionId=this.generateSessionId()}catch(e){this.consentGiven={[rt]:!0,[at]:!1,[ct]:!1,[lt]:!1,timestamp:null},this.sessionId="fallback_"+Date.now()}}setCookie(e,t,n={}){const i=dt[e];if(!i)return!1;if(!this.hasConsentForCategory(i.category))return!1;const s=new Date;s.setTime(s.getTime()+24*i.expireDays*60*60*1e3);const o=`${i.name}=${encodeURIComponent(JSON.stringify(t))}; expires=${s.toUTCString()}; path=/; SameSite=Lax${n.secure?"; Secure":""}`;return document.cookie=o,!0}getCookie(e){const t=dt[e];if(!t)return null;const n=t.name+"=",i=decodeURIComponent(document.cookie).split(";");for(let o=0;o<i.length;o++){let e=i[o];for(;" "===e.charAt(0);)e=e.substring(1);if(0===e.indexOf(n))try{return JSON.parse(e.substring(n.length,e.length))}catch(s){return e.substring(n.length,e.length)}}return null}deleteCookie(e){const t=dt[e];t&&(document.cookie=`${t.name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`)}hasConsentForCategory(e){if(e===rt)return!0;return!0===this.getConsentPreferences()[e]}getConsentPreferences(){const e=this.getRawCookie("researchsat_consent");if(!e)return{[rt]:!0,[at]:!1,[ct]:!1,[lt]:!1,timestamp:null};try{return JSON.parse(e)}catch(t){return{[rt]:!0,[at]:!1,[ct]:!1,[lt]:!1,timestamp:null}}}setConsentPreferences(e){const t={...e,timestamp:(new Date).toISOString()},n=new Date;n.setTime(n.getTime()+31536e6),document.cookie=`researchsat_consent=${encodeURIComponent(JSON.stringify(t))}; expires=${n.toUTCString()}; path=/; SameSite=Lax`,this.consentGiven=t,this.cleanupNonConsentedCookies(e)}cleanupNonConsentedCookies(e){Object.entries(dt).forEach((([t,n])=>{n.category===rt||e[n.category]||this.deleteCookie(t)}))}getRawCookie(e){const t=e+"=",n=document.cookie.split(";");for(let i=0;i<n.length;i++){let e=n[i];for(;" "===e.charAt(0);)e=e.substring(1,e.length);if(0===e.indexOf(t))return decodeURIComponent(e.substring(t.length,e.length))}return null}generateSessionId(){return"sess_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}initializeSession(){if(this.hasConsentForCategory(at)){this.getCookie("SESSION_ID")||this.setCookie("SESSION_ID",{id:this.sessionId,startTime:(new Date).toISOString(),pageViews:0,events:[]})}}getCookiesByCategory(e){return Object.entries(dt).filter((([t,n])=>n.category===e)).map((([e,t])=>({key:e,name:t.name,value:this.getCookie(e),description:t.description})))}clearAllCookies(){Object.keys(dt).forEach((e=>{this.deleteCookie(e)}))}},ht={PAGE_VIEW:"page_view",BUTTON_CLICK:"button_click",FORM_INTERACTION:"form_interaction",FORM_SUBMISSION:"form_submission",SCROLL_DEPTH:"scroll_depth",TIME_ON_PAGE:"time_on_page",CAL_BOOKING:"cal_booking",DOWNLOAD:"download",EMAIL_SIGNUP:"email_signup",CONTACT_ATTEMPT:"contact_attempt",INTEREST_SIGNAL:"interest_signal"},pt={"/payloads":10,"/missions":8,"/contact":15,"/book-mission":20,"/partnerships":12,"/about":5,"/features":7},mt={PAYLOADS:["payloads","features"],MISSIONS:["missions","past-missions"],PARTNERSHIPS:["partnerships","contact"],RESEARCH:["about","news"],COMMERCIAL:["book-mission","spacexperiment"]};const ft=new class{constructor(){try{this.sessionStartTime=Date.now(),this.currentPageStartTime=Date.now(),this.scrollDepthTracked=new Set,this.engagementScore=0,this.userJourney=[],this.initializeTracking()}catch(e){this.sessionStartTime=Date.now(),this.currentPageStartTime=Date.now(),this.scrollDepthTracked=new Set,this.engagementScore=0,this.userJourney=[]}}initializeTracking(){ut.hasConsentForCategory(at)&&(this.initializeSession(),this.setupScrollTracking(),this.setupEngagementTracking(),this.loadUserJourney())}initializeSession(){ut.initializeSession();const e=ut.getCookie("SESSION_ID")||{id:ut.sessionId,startTime:(new Date).toISOString(),pageViews:0,events:[]};e.lastActivity=(new Date).toISOString(),ut.setCookie("SESSION_ID",e)}trackPageView(e,t={}){try{if(!ut.hasConsentForCategory(at))return;const n={type:ht.PAGE_VIEW,path:e,timestamp:(new Date).toISOString(),referrer:document.referrer,userAgent:navigator.userAgent,screenResolution:`${screen.width}x${screen.height}`,viewportSize:`${window.innerWidth}x${window.innerHeight}`,...t},i=ut.getCookie("SESSION_ID");i&&(i.pageViews+=1,i.events.push(n),i.lastActivity=(new Date).toISOString(),ut.setCookie("SESSION_ID",i)),this.updateUserJourney(e),this.updateEngagementScore(e),this.updateInterestProfile(e),this.currentPageStartTime=Date.now(),this.scrollDepthTracked.clear(),"undefined"!=typeof gtag&&gtag("config","G-M2YTMHZ1JG",{page_title:t.title||document.title,page_location:t.url||window.location.href})}catch(n){}}trackEvent(e,t={}){if(!ut.hasConsentForCategory(at))return;const n={type:e,timestamp:(new Date).toISOString(),path:window.location.pathname,...t},i=ut.getCookie("SESSION_ID");i&&(i.events.push(n),i.lastActivity=(new Date).toISOString(),ut.setCookie("SESSION_ID",i)),[ht.FORM_SUBMISSION,ht.CAL_BOOKING,ht.EMAIL_SIGNUP].includes(e)&&this.updateEngagementScore(null,25),"undefined"!=typeof gtag&&gtag("event",e,{event_category:"engagement",event_label:t.formId||t.source||"unknown",value:this.getEventValue(e)})}trackFormInteraction(e,t,n){this.trackEvent(ht.FORM_INTERACTION,{formId:e,field:t,action:n,value:"change"===n?"filled":null})}trackFormSubmission(e,t={}){this.trackEvent(ht.FORM_SUBMISSION,{formId:e,fields:Object.keys(t),hasEmail:!!t.email,hasPhone:!!t.phone,hasCompany:!!t.organization||!!t.company}),this.updateEngagementScore(null,50)}trackCalBooking(e,t="opened"){this.trackEvent(ht.CAL_BOOKING,{calLink:e,action:t}),"completed"===t&&this.updateEngagementScore(null,100)}setupScrollTracking(){let e=!1;const t=()=>{const t=window.pageYOffset,n=document.documentElement.scrollHeight-window.innerHeight,i=Math.round(t/n*100);[25,50,75,90].forEach((e=>{i>=e&&!this.scrollDepthTracked.has(e)&&(this.scrollDepthTracked.add(e),this.trackEvent(ht.SCROLL_DEPTH,{depth:e,path:window.location.pathname}),this.updateEngagementScore(null,e/10))})),e=!1};window.addEventListener("scroll",(()=>{e||(requestAnimationFrame(t),e=!0)}),{passive:!0})}setupEngagementTracking(){window.addEventListener("beforeunload",(()=>{const e=Date.now()-this.currentPageStartTime;this.trackEvent(ht.TIME_ON_PAGE,{duration:e,path:window.location.pathname})})),document.addEventListener("click",(e=>{var t,n;const i=e.target.closest("a, button, [data-track]");if(i){const e={element:i.tagName.toLowerCase(),text:null==(t=i.textContent)?void 0:t.trim().substring(0,50),href:i.href,className:i.className,dataTrack:i.getAttribute("data-track")};this.trackEvent(ht.BUTTON_CLICK,e),((null==(n=i.href)?void 0:n.includes("cal.com"))||i.getAttribute("data-cal-link"))&&this.trackCalBooking(i.getAttribute("data-cal-link")||i.href,"opened")}}))}updateUserJourney(e){let t=ut.getCookie("USER_JOURNEY")||{pages:[],firstVisit:(new Date).toISOString(),totalVisits:0};t.pages.push({path:e,timestamp:(new Date).toISOString(),sessionId:ut.sessionId}),t.pages.length>50&&(t.pages=t.pages.slice(-50)),t.lastVisit=(new Date).toISOString(),t.totalVisits+=1,ut.setCookie("USER_JOURNEY",t),this.userJourney=t}updateEngagementScore(e=null,t=0){let n=ut.getCookie("ENGAGEMENT_SCORE")||{score:0,lastUpdated:(new Date).toISOString(),factors:{}};e&&pt[e]&&(n.score+=pt[e],n.factors[e]=(n.factors[e]||0)+pt[e]),n.score+=t,n.lastUpdated=(new Date).toISOString(),n.score=Math.min(n.score,1e3),ut.setCookie("ENGAGEMENT_SCORE",n),this.engagementScore=n.score}updateInterestProfile(e){if(!ut.hasConsentForCategory(lt))return;let t=ut.getCookie("INTEREST_PROFILE")||{categories:{},lastUpdated:(new Date).toISOString()};Object.entries(mt).forEach((([n,i])=>{i.some((t=>e.includes(t)))&&(t.categories[n]=(t.categories[n]||0)+1)})),t.lastUpdated=(new Date).toISOString(),ut.setCookie("INTEREST_PROFILE",t)}loadUserJourney(){this.userJourney=ut.getCookie("USER_JOURNEY")||{pages:[]}}getEventValue(e){return{[ht.FORM_SUBMISSION]:50,[ht.CAL_BOOKING]:100,[ht.EMAIL_SIGNUP]:30,[ht.DOWNLOAD]:20,[ht.SCROLL_DEPTH]:10,[ht.BUTTON_CLICK]:5}[e]||1}getAnalyticsSummary(){return{sessionId:ut.sessionId,engagementScore:this.engagementScore,userJourney:this.userJourney,sessionData:ut.getCookie("SESSION_ID"),interestProfile:ut.getCookie("INTEREST_PROFILE"),consentGiven:ut.getConsentPreferences()}}},gt={PAGES:{"/contact":25,"/book-mission":30,"/payloads":20,"/partnerships":25,"/missions":15,"/spacexperiment":20,"/about":10,"/features":12,"/careers":5},ACTIONS:{FORM_SUBMISSION:50,CAL_BOOKING_OPENED:40,CAL_BOOKING_COMPLETED:100,EMAIL_SIGNUP:30,DEEP_SCROLL:10,LONG_SESSION:20},COMPANY_SIZE:{ENTERPRISE:50,LARGE:30,MEDIUM:20,SMALL:10,UNKNOWN:0},INDUSTRY:{BIOTECH:40,PHARMACEUTICAL:45,AEROSPACE:35,RESEARCH_INSTITUTION:30,UNIVERSITY:25,GOVERNMENT:35,STARTUP:20,OTHER:10}},_t={BIOTECH:["biotech","bio","genetics","genomics","pharma"],PHARMACEUTICAL:["pharma","pharmaceutical","drug","medicine"],AEROSPACE:["aerospace","space","aviation","satellite"],RESEARCH_INSTITUTION:["research","institute","lab","laboratory"],UNIVERSITY:["edu","university","college","academic"],GOVERNMENT:["gov","government","nasa","esa","agency"]},vt={"nasa.gov":"ENTERPRISE","spacex.com":"ENTERPRISE","boeing.com":"ENTERPRISE","pfizer.com":"ENTERPRISE","novartis.com":"ENTERPRISE","roche.com":"ENTERPRISE"};const bt=new class{constructor(){this.currentScore=0,this.leadProfile=null,this.loadLeadProfile()}calculateLeadScore(){if(!ut.hasConsentForCategory(at))return{score:0,factors:{}};let e=0;const t={},n=ut.getCookie("USER_JOURNEY");if(null==n?void 0:n.pages){const i=this.calculatePageScores(n.pages);e+=i.total,t.pageVisits=i}const i=ut.getCookie("ENGAGEMENT_SCORE");i&&(e+=Math.min(i.score,200),t.engagement=i.score);const s=ut.getCookie("SESSION_ID");if(null==s?void 0:s.events){const n=this.calculateActionScores(s.events);e+=n.total,t.actions=n}const o=this.getCompanyProfile();if(o){const n=this.calculateCompanyScore(o);e+=n.total,t.company=n}const r=this.calculateBehaviorScore();e+=r.total,t.behavior=r;const a=this.calculateRecencyScore(n);return e+=a.total,t.recency=a,this.currentScore=Math.min(e,1e3),this.updateLeadProfile({score:this.currentScore,factors:t,lastCalculated:(new Date).toISOString()}),{score:this.currentScore,factors:t,qualification:this.getLeadQualification(this.currentScore),recommendations:this.getRecommendations(t)}}calculatePageScores(e){const t={};let n=0;e.forEach((e=>{const i=gt.PAGES[e.path]||0;t[e.path]=(t[e.path]||0)+i,n+=i}));const i=Object.keys(t).filter((e=>gt.PAGES[e]>=20));return i.length>=3&&(n+=25),{total:n,breakdown:t,diversity:i.length}}calculateActionScores(e){const t={};let n=0;return e.forEach((e=>{let i=0;switch(e.type){case"form_submission":i=gt.ACTIONS.FORM_SUBMISSION;break;case"cal_booking":i="completed"===e.action?gt.ACTIONS.CAL_BOOKING_COMPLETED:gt.ACTIONS.CAL_BOOKING_OPENED;break;case"email_signup":i=gt.ACTIONS.EMAIL_SIGNUP;break;case"scroll_depth":e.depth>=75&&(i=gt.ACTIONS.DEEP_SCROLL);break;case"time_on_page":e.duration>12e4&&(i=gt.ACTIONS.LONG_SESSION)}i>0&&(t[e.type]=(t[e.type]||0)+i,n+=i)})),{total:n,breakdown:t}}calculateCompanyScore(e){let t=0;const n={};if(e.industry){const i=gt.INDUSTRY[e.industry]||0;t+=i,n.industry=i}if(e.size){const i=gt.COMPANY_SIZE[e.size]||0;t+=i,n.size=i}if(e.emailDomain){const i=this.calculateDomainScore(e.emailDomain);t+=i,n.domain=i}return{total:t,breakdown:n}}calculateBehaviorScore(){let e=0;const t={},n=ut.getCookie("USER_JOURNEY");if(n){if(n.totalVisits>1){const i=Math.min(5*n.totalVisits,30);e+=i,t.returnVisitor=i}const i=ut.getCookie("SESSION_ID");if(null==i?void 0:i.events){const n=i.events.length;if(n>10){const i=Math.min(2*n,20);e+=i,t.sessionLength=i}}}return{total:e,breakdown:t}}calculateRecencyScore(e){let t=0;const n={};if(null==e?void 0:e.lastVisit){const i=(Date.now()-new Date(e.lastVisit))/864e5;i<1?(t+=15,n.recentActivity=15):i<7&&(t+=10,n.recentActivity=10)}return{total:t,breakdown:n}}calculateDomainScore(e){return vt[e]?20:e.endsWith(".gov")||e.endsWith(".edu")?15:e.endsWith(".com")||e.endsWith(".org")?5:0}getLeadQualification(e){return e>=300?"HOT":e>=200?"WARM":e>=100?"QUALIFIED":e>=50?"INTERESTED":"COLD"}getRecommendations(e){var t,n,i,s,o;const r=[];return e.engagement>100&&!(null==(t=e.actions)?void 0:t.form_submission)&&r.push({type:"CONTACT_FORM",priority:"HIGH",message:"Show contact form popup - high engagement detected"}),(null==(i=null==(n=e.pageVisits)?void 0:n.breakdown)?void 0:i["/payloads"])&&!(null==(s=e.actions)?void 0:s.cal_booking)&&r.push({type:"CAL_BOOKING",priority:"MEDIUM",message:"Promote consultation booking - payload interest detected"}),(null==(o=e.behavior)?void 0:o.returnVisitor)&&e.score<200&&r.push({type:"RETARGETING",priority:"MEDIUM",message:"Target with email campaign - return visitor"}),r}updateCompanyProfile(e){if(!ut.hasConsentForCategory(lt))return;let t=ut.getCookie("COMPANY_PROFILE")||{};if(e.email){const n=e.email.split("@")[1];t.emailDomain=n,t.industry=this.detectIndustryFromDomain(n),t.size=vt[n]||"UNKNOWN"}(e.organization||e.company)&&(t.companyName=e.organization||e.company),t.lastUpdated=(new Date).toISOString(),ut.setCookie("COMPANY_PROFILE",t)}detectIndustryFromDomain(e){for(const[t,n]of Object.entries(_t))if(n.some((t=>e.toLowerCase().includes(t))))return t;return"OTHER"}getCompanyProfile(){return ut.getCookie("COMPANY_PROFILE")}loadLeadProfile(){this.leadProfile=ut.getCookie("LEAD_PROFILE")||{score:0,qualification:"COLD",firstSeen:(new Date).toISOString()}}updateLeadProfile(e){this.leadProfile={...this.leadProfile,...e,lastUpdated:(new Date).toISOString()},ut.hasConsentForCategory(at)&&ut.setCookie("LEAD_PROFILE",this.leadProfile)}getLeadSummary(){var e;const t=this.calculateLeadScore(),n=this.getCompanyProfile(),i=ut.getCookie("USER_JOURNEY"),s=ut.getCookie("SESSION_ID");return{score:t.score,qualification:t.qualification,companyProfile:n,visitHistory:null==(e=null==i?void 0:i.pages)?void 0:e.slice(-10),totalVisits:(null==i?void 0:i.totalVisits)||0,lastActivity:null==s?void 0:s.lastActivity,recommendations:t.recommendations,factors:t.factors}}},yt={HERO_MESSAGES:{BIOTECH:{title:"Accelerate Drug Discovery in Microgravity",subtitle:"Leverage space-based research to unlock breakthrough pharmaceutical innovations",cta:"Explore Biotech Solutions"},AEROSPACE:{title:"Advanced Space Research Platforms",subtitle:"Custom satellite payloads for cutting-edge aerospace research and development",cta:"View Mission Capabilities"},UNIVERSITY:{title:"Educational Space Research Opportunities",subtitle:"Affordable access to space-based experiments for academic institutions",cta:"Academic Programs"},GOVERNMENT:{title:"Strategic Space Research Partnerships",subtitle:"Secure, reliable space-based research solutions for government agencies",cta:"Government Solutions"},DEFAULT:{title:"Pioneering Space-Based Research",subtitle:"Transform your research with the unique advantages of microgravity",cta:"Discover Possibilities"}},SERVICE_HIGHLIGHTS:{BIOTECH:[{title:"Protein Crystallization",description:"Enhanced crystal formation in microgravity for drug development",icon:"fas fa-dna"},{title:"Cell Culture Studies",description:"Advanced cellular research in space environments",icon:"fas fa-microscope"},{title:"Drug Formulation",description:"Novel pharmaceutical development opportunities",icon:"fas fa-pills"}],AEROSPACE:[{title:"Custom Payloads",description:"Tailored satellite systems for specific research needs",icon:"fas fa-satellite"},{title:"Mission Planning",description:"End-to-end mission design and execution",icon:"fas fa-rocket"},{title:"Data Analysis",description:"Comprehensive post-mission data processing",icon:"fas fa-chart-line"}],DEFAULT:[{title:"Microgravity Research",description:"Unique research opportunities in space environments",icon:"fas fa-globe"},{title:"Custom Solutions",description:"Tailored approaches for your specific research needs",icon:"fas fa-cogs"},{title:"Expert Support",description:"Dedicated team of space research specialists",icon:"fas fa-users"}]},CASE_STUDIES:{BIOTECH:[{title:"Accelerated Protein Crystallization",industry:"Pharmaceutical",result:"300% improvement in crystal quality",image:"/src/assets/images/case-studies/protein-crystal.jpg"},{title:"Novel Drug Formulation",industry:"Biotech Startup",result:"Breakthrough in cancer treatment",image:"/src/assets/images/case-studies/drug-formulation.jpg"}],AEROSPACE:[{title:"Advanced Materials Testing",industry:"Aerospace Corporation",result:"New alloy development for spacecraft",image:"/src/assets/images/case-studies/materials-testing.jpg"},{title:"Satellite Component Validation",industry:"Defense Contractor",result:"Improved component reliability",image:"/src/assets/images/case-studies/satellite-testing.jpg"}],DEFAULT:[{title:"Microgravity Research Success",industry:"Research Institution",result:"Groundbreaking scientific discoveries",image:"/src/assets/images/case-studies/research-success.jpg"}]}},wt={name:"Enterprise",description:"Full-service space research solutions",features:["Custom payload design","Dedicated mission support","Priority scheduling"],cta:"Contact Sales"},Et={name:"Academic",description:"Special pricing for educational institutions",features:["Educational discounts","Shared mission opportunities","Student programs"],cta:"Academic Inquiry"},xt={name:"Innovation",description:"Flexible solutions for emerging companies",features:["Scalable options","Milestone-based pricing","Technical mentorship"],cta:"Startup Program"},Ct={name:"Standard",description:"Professional space research services",features:["Expert consultation","Mission planning","Data analysis"],cta:"Get Quote"};const St=new class{constructor(){this.userSegment=null,this.interestProfile=null,this.engagementLevel="LOW",this.loadUserProfile()}loadUserProfile(){if(!ut.hasConsentForCategory(lt))return;const e=ut.getCookie("COMPANY_PROFILE"),t=ut.getCookie("INTEREST_PROFILE"),n=ut.getCookie("ENGAGEMENT_SCORE");this.userSegment=this.determineUserSegment(e),this.interestProfile=t,this.engagementLevel=this.determineEngagementLevel((null==n?void 0:n.score)||0)}determineUserSegment(e){if(!e)return"DEFAULT";if(e.industry)switch(e.industry){case"BIOTECH":case"PHARMACEUTICAL":return"BIOTECH";case"AEROSPACE":return"AEROSPACE";case"UNIVERSITY":case"RESEARCH_INSTITUTION":return"UNIVERSITY";case"GOVERNMENT":return"GOVERNMENT";default:return"DEFAULT"}if(e.emailDomain){if(e.emailDomain.includes("edu"))return"UNIVERSITY";if(e.emailDomain.includes("gov"))return"GOVERNMENT";if(e.emailDomain.includes("bio")||e.emailDomain.includes("pharma"))return"BIOTECH";if(e.emailDomain.includes("aerospace")||e.emailDomain.includes("space"))return"AEROSPACE"}return"DEFAULT"}determineEngagementLevel(e){return e>=200?"HIGH":e>=100?"MEDIUM":"LOW"}getPersonalizedHero(){const e=this.userSegment||"DEFAULT";return yt.HERO_MESSAGES[e]||yt.HERO_MESSAGES.DEFAULT}getPersonalizedServices(){const e=this.userSegment||"DEFAULT";return yt.SERVICE_HIGHLIGHTS[e]||yt.SERVICE_HIGHLIGHTS.DEFAULT}getPersonalizedCaseStudies(){const e=this.userSegment||"DEFAULT";return yt.CASE_STUDIES[e]||yt.CASE_STUDIES.DEFAULT}getPersonalizedPricing(){var e;const t=ut.getCookie("COMPANY_PROFILE");return"UNIVERSITY"===(null==t?void 0:t.industry)||(null==(e=null==t?void 0:t.emailDomain)?void 0:e.includes("edu"))?Et:"ENTERPRISE"===(null==t?void 0:t.size)?wt:"SMALL"===(null==t?void 0:t.size)||"STARTUP"===(null==t?void 0:t.industry)?xt:Ct}getPersonalizedCTA(){const e=bt.getLeadSummary();return"HOT"===e.qualification?{text:"Schedule Immediate Consultation",urgency:"HIGH",style:"primary-urgent"}:"WARM"===e.qualification?{text:"Book Your Strategy Session",urgency:"MEDIUM",style:"primary"}:"HIGH"===this.engagementLevel?{text:"Get Custom Proposal",urgency:"MEDIUM",style:"secondary"}:{text:"Learn More",urgency:"LOW",style:"default"}}getRecommendedContent(){var e;if(!(null==(e=this.interestProfile)?void 0:e.categories))return[];const t=[],n=this.interestProfile.categories;return Object.entries(n).sort((([,e],[,t])=>t-e)).slice(0,3).forEach((([e,n])=>{switch(e){case"PAYLOADS":t.push({title:"Advanced Payload Solutions",description:"Explore our cutting-edge payload technologies",link:"/payloads",priority:n});break;case"MISSIONS":t.push({title:"Mission Success Stories",description:"See how we've helped others achieve their research goals",link:"/past-missions",priority:n});break;case"PARTNERSHIPS":t.push({title:"Partnership Opportunities",description:"Discover collaboration possibilities",link:"/partnerships",priority:n})}})),t.sort(((e,t)=>t.priority-e.priority))}getExitIntentOffer(){const e=bt.getLeadSummary();return"HOT"===e.qualification||"WARM"===e.qualification?{title:"Wait! Let's discuss your project",description:"Schedule a free 30-minute consultation with our space research experts",offer:"Free Consultation",cta:"Schedule Now"}:"MEDIUM"===this.engagementLevel||"HIGH"===this.engagementLevel?{title:"Get Your Custom Research Proposal",description:"Download our comprehensive guide to space-based research opportunities",offer:"Free Research Guide",cta:"Download Now"}:{title:"Stay Updated on Space Research",description:"Join our newsletter for the latest in space-based research innovations",offer:"Newsletter Signup",cta:"Subscribe"}}getNavigationRecommendations(){const e=[],t=ut.getCookie("USER_JOURNEY");if(!(null==t?void 0:t.pages))return e;const n=new Set(t.pages.map((e=>e.path)));return n.has("/payloads")||e.push({text:"Explore Payloads",link:"/payloads",priority:10}),n.has("/contact")||"LOW"===this.engagementLevel||e.push({text:"Contact Us",link:"/contact",priority:8}),n.has("/partnerships")||"DEFAULT"===this.userSegment||e.push({text:"Partnership Options",link:"/partnerships",priority:7}),e.sort(((e,t)=>t.priority-e.priority)).slice(0,3)}updatePersonalization(e,t={}){ut.hasConsentForCategory(lt)&&("form_submission"===e&&t.formData&&bt.updateCompanyProfile(t.formData),this.loadUserProfile())}getPersonalizationData(){return{userSegment:this.userSegment,engagementLevel:this.engagementLevel,hero:this.getPersonalizedHero(),services:this.getPersonalizedServices(),caseStudies:this.getPersonalizedCaseStudies(),pricing:this.getPersonalizedPricing(),cta:this.getPersonalizedCTA(),recommendations:this.getRecommendedContent(),exitOffer:this.getExitIntentOffer(),navigation:this.getNavigationRecommendations()}}},Tt=e=>{let t;const n=new Set,i=(e,i)=>{const s="function"==typeof e?e(t):e;if(!Object.is(s,t)){const e=t;t=(null!=i?i:"object"!=typeof s||null===s)?s:Object.assign({},t,s),n.forEach((n=>n(t,e)))}},s=()=>t,o={setState:i,getState:s,getInitialState:()=>r,subscribe:e=>(n.add(e),()=>n.delete(e))},r=t=e(i,s,o);return o},At=e=>e;const Ot=e=>{const t=(e=>e?Tt(e):Tt)(e),n=e=>function(e,t=At){const n=c.useSyncExternalStore(e.subscribe,(()=>t(e.getState())),(()=>t(e.getInitialState())));return c.useDebugValue(n),n}(t,e);return Object.assign(n,t),n};function kt(e,t){let n;try{n=e()}catch(i){return}return{getItem:e=>{var t;const i=e=>null===e?null:JSON.parse(e,void 0),s=null!=(t=n.getItem(e))?t:null;return s instanceof Promise?s.then(i):i(s)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}const jt=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then:e=>jt(e)(n),catch(e){return this}}}catch(n){return{then(e){return this},catch:e=>jt(e)(n)}}},Nt=(Lt=((e,t)=>(n,i,s)=>{let o={storage:kt((()=>localStorage)),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},r=!1;const a=new Set,c=new Set;let l=o.storage;if(!l)return e(((...e)=>{n(...e)}),i,s);const d=()=>{const e=o.partialize({...i()});return l.setItem(o.name,{state:e,version:o.version})},u=s.setState;s.setState=(e,t)=>{u(e,t),d()};const h=e(((...e)=>{n(...e),d()}),i,s);let p;s.getInitialState=()=>h;const m=()=>{var e,t;if(!l)return;r=!1,a.forEach((e=>{var t;return e(null!=(t=i())?t:h)}));const s=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=i())?e:h))||void 0;return jt(l.getItem.bind(l))(o.name).then((e=>{if(e){if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];if(o.migrate){const t=o.migrate(e.state,e.version);return t instanceof Promise?t.then((e=>[!0,e])):[!0,t]}}return[!1,void 0]})).then((e=>{var t;const[s,r]=e;if(p=o.merge(r,null!=(t=i())?t:h),n(p,!0),s)return d()})).then((()=>{null==s||s(p,void 0),p=i(),r=!0,c.forEach((e=>e(p)))})).catch((e=>{null==s||s(void 0,e)}))};return s.persist={setOptions:e=>{o={...o,...e},e.storage&&(l=e.storage)},clearStorage:()=>{null==l||l.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>m(),hasHydrated:()=>r,onHydrate:e=>(a.add(e),()=>{a.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||m(),p||h})(((e,t)=>({isLoading:!1,setIsLoading:t=>e({isLoading:t}),activeModal:null,setActiveModal:t=>e({activeModal:t}),closeModal:()=>e({activeModal:null}),notifications:[],addNotification:t=>e((e=>({notifications:[...e.notifications,{id:Date.now(),type:"info",duration:3e3,...t}]}))),removeNotification:t=>e((e=>({notifications:e.notifications.filter((e=>e.id!==t))}))),clearNotifications:()=>e({notifications:[]}),formSubmissions:{},trackFormSubmission:t=>e((e=>{var n;return{formSubmissions:{...e.formSubmissions,[t]:{lastSubmitted:(new Date).toISOString(),count:((null==(n=e.formSubmissions[t])?void 0:n.count)||0)+1}}}})),pageViews:{},trackPageView:t=>e((e=>({pageViews:{...e.pageViews,[t]:(e.pageViews[t]||0)+1}}))),featureFlags:{enableNewHeader:!1,enableNewFooter:!1,enableNewContactForm:!1},setFeatureFlag:(t,n)=>e((e=>({featureFlags:{...e.featureFlags,[t]:n}})))})),{name:"researchsat-app-store",partialize:e=>({featureFlags:e.featureFlags,pageViews:e.pageViews,formSubmissions:e.formSubmissions})}))?Ot(Lt):Ot;var Lt;const It=()=>{const[e,t]=a.useState(""),[n,i]=a.useState(!1),[s,o]=a.useState(!1),r=Nt((e=>e.addNotification));return w.jsx("footer",{className:Ae,children:w.jsxs("div",{className:"container",children:[w.jsx("div",{className:Oe,children:w.jsx("img",{src:"/assets/png/ResearchSatLogo-DQHoConw.png",alt:"ResearchSat Logo",className:ke})}),w.jsxs("div",{className:je,children:[w.jsxs("div",{className:Ne,children:[w.jsxs("div",{className:De,children:[w.jsx("p",{className:Ie,children:"9 Light Square,"}),w.jsx("p",{className:Ie,children:"Adelaide, SA 5000"})]}),w.jsx("p",{className:Me,children:"<EMAIL>"}),w.jsx("p",{className:Me,children:"+61 124.459.8900"})]}),w.jsxs("div",{className:Ne,children:[w.jsx(l,{to:"/",className:Pe,children:"Home"}),w.jsx(l,{to:"/about",className:Pe,children:"About"}),w.jsx(l,{to:"/missions",className:Pe,children:"Mission"}),w.jsx(l,{to:"/terms-conditions",className:Pe,children:"Terms & Conditions"})]}),w.jsxs("div",{className:Ne,children:[w.jsx(l,{to:"/news",className:Pe,children:"News"}),w.jsx(l,{to:"/careers",className:Pe,children:"Careers"}),w.jsx(l,{to:"/contact",className:Pe,children:"Contact"}),w.jsx(l,{to:"/privacy-policy",className:Pe,children:"Privacy Policy"})]}),w.jsxs("div",{className:Ne,children:[w.jsx(l,{to:"/payloads",className:Pe,children:"Payloads"}),w.jsx(l,{to:"/past-missions",className:Pe,children:"Past Missions"}),w.jsx(l,{to:"/microgravity-guide",className:Pe,children:"Research Guide"}),w.jsx(l,{to:"/gallery",className:Pe,children:"Gallery"})]}),w.jsxs("div",{className:Ne,children:[w.jsx("h3",{className:Le,children:"Subscribe to our newsletter"}),w.jsx("p",{className:Ie,children:"Stay updated on our latest missions &"}),w.jsx("p",{className:Ie,children:"advancements"}),w.jsx("p",{className:Ye,children:"Email ID"}),w.jsxs("form",{onSubmit:async n=>{n.preventDefault(),i(!0);try{ft.trackEvent("email_signup",{email:e,source:"footer_newsletter",timestamp:(new Date).toISOString()}),bt.updateCompanyProfile({email:e}),St.updatePersonalization("email_signup",{formData:{email:e},source:"footer_newsletter"}),"undefined"!=typeof gtag&&gtag("event","newsletter_signup",{event_category:"engagement",event_label:"footer_newsletter",value:1}),await new Promise((e=>setTimeout(e,1e3))),r({type:"success",message:"🚀 Welcome aboard! You're now subscribed to ResearchSat updates.",duration:5e3}),o(!0),setTimeout((()=>{t(""),o(!1)}),3e3)}catch(s){r({type:"error",message:"Subscription failed. Please try again.",duration:4e3})}finally{i(!1)}},className:Ze,children:[w.jsxs("div",{className:Ke,children:[w.jsx("input",{type:"email",placeholder:"Enter Email ID",className:`${Je} ${s?Qe:""}`,value:e,onChange:e=>t(e.target.value),disabled:n||s,required:!0,onFocus:()=>{ft.trackFormInteraction("footer-newsletter","email","focus")}}),w.jsx("button",{type:"submit",className:`${Xe} ${n?et:""} ${s?tt:""}`,disabled:n||s,children:s?w.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:w.jsx("path",{d:"M20 6L9 17L4 12",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}):n?w.jsx("div",{className:nt}):w.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:w.jsx("path",{d:"M5 12H19M19 12L12 5M19 12L12 19",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),s&&w.jsxs("div",{className:it,children:[w.jsx("span",{className:st,children:"🚀"}),w.jsx("span",{className:ot,children:"Successfully subscribed!"})]})]})]})]}),w.jsx("div",{className:Re}),w.jsxs("div",{className:He,children:[w.jsxs("div",{className:$e,children:["Copyright © ",(new Date).getFullYear()," ResearchSat - All rights reserved"]}),w.jsxs("div",{className:Fe,children:[w.jsx("div",{className:Be,children:"Reach us"}),w.jsxs("div",{className:Ve,children:[w.jsx("a",{href:"https://www.facebook.com/researchsat/",target:"_blank",rel:"noopener noreferrer",className:Ue,children:w.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='33'%20height='34'%20viewBox='0%200%2033%2034'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M26.8005%200.332031C30.2275%200.332031%2033%203.10126%2033%206.52434V27.1397C33%2030.5628%2030.2275%2033.332%2026.8005%2033.332H22.7958V20.5243H27.07L27.6861%2015.5628H22.7958V12.3705C22.7958%2010.9474%2023.1809%209.98588%2025.2602%209.98588L27.8786%209.94742V5.52434C27.4166%205.44742%2025.8378%205.33203%2024.028%205.33203C20.2544%205.33203%2017.6359%207.63972%2017.6359%2011.909V15.5628H13.3232V20.5243H17.6359V33.332H6.19953C2.77246%2033.332%200%2030.5628%200%2027.1397V6.52434C0%203.10126%202.77246%200.332031%206.19953%200.332031H26.8005Z'%20fill='%23636363'/%3e%3c/svg%3e",alt:"Facebook",className:ze})}),w.jsx("a",{href:"https://twitter.com/researchsat",target:"_blank",rel:"noopener noreferrer",className:Ue,children:w.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='30'%20height='30'%20viewBox='0%200%2030%2030'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_180_31482)'%3e%3cpath%20d='M17.7926%2012.6117L28.7205%200.332031H26.1307L16.6423%2010.9941L9.06352%200.332031H0.322266L11.7827%2016.4551L0.322266%2029.332H2.91211L12.9326%2018.0726L20.936%2029.332H29.6773L17.7919%2012.6117H17.7926ZM14.2455%2016.597L13.0842%2014.9915L3.84516%202.21658H7.82297L15.2787%2012.5265L16.4398%2014.132L26.1319%2027.5331H22.1545L14.2455%2016.5976V16.597Z'%20fill='%23636363'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_180_31482'%3e%3crect%20width='30'%20height='29'%20fill='white'%20transform='translate(0%200.332031)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",alt:"Twitter",className:Ge})}),w.jsx("a",{href:"https://www.instagram.com/researchsat/",target:"_blank",rel:"noopener noreferrer",className:Ue,children:w.jsx("img",{src:"/assets/svg/instagram-Bz-HIF1W.svg",alt:"Instagram",className:We})}),w.jsx("a",{href:"https://www.linkedin.com/company/researchsat/",target:"_blank",rel:"noopener noreferrer",className:Ue,children:w.jsx("img",{src:"data:image/svg+xml,%3csvg%20width='32'%20height='33'%20viewBox='0%200%2032%2033'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_180_31483)'%3e%3cpath%20d='M4.92882%2027.1106H9.74562V12.677H4.92882V27.1106ZM10.0817%208.20149C10.0443%206.78425%209.03617%205.70266%207.39323%205.70266C5.75029%205.70266%204.63011%206.78425%204.63011%208.20149C4.63011%209.58145%205.67561%2010.7003%207.31855%2010.7003C9.03617%2010.7003%2010.0817%209.58145%2010.0817%208.20149ZM22.2544%2027.1106H27.0712V18.8309C27.0712%2014.3926%2024.6814%2012.3414%2021.5449%2012.3414C18.9312%2012.3414%2017.811%2013.7586%2017.1762%2014.7656H17.2135V12.677H12.3967C12.3967%2012.677%2012.4714%2014.0197%2012.3967%2027.1106H17.2135V19.0173C17.2135%2018.6071%2017.2509%2018.1968%2017.3629%2017.8612C17.699%2017.0034%2018.5204%2016.1083%2019.8273%2016.1083C21.5823%2016.1083%2022.2544%2017.4136%2022.2544%2019.3903V27.1106ZM32%206.33669V26.3274C32%2029.6467%2029.3116%2032.332%2025.9883%2032.332H6.01167C2.68845%2032.332%200%2029.6467%200%2026.3274V6.33669C0%203.01735%202.68845%200.332031%206.01167%200.332031H25.9883C29.3116%200.332031%2032%203.01735%2032%206.33669Z'%20fill='%23636363'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_180_31483'%3e%3crect%20width='32'%20height='32'%20fill='white'%20transform='translate(0%200.332031)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",alt:"LinkedIn",className:qe})})]})]})]})]})})},Pt="_loadingContainer_1ozwo_2",Dt="_spinner_1ozwo_11",Mt="_doubleBounce1_1ozwo_18",Rt="_doubleBounce2_1ozwo_18",Ht="_loadingText_1ozwo_34",$t=()=>w.jsxs("div",{className:Pt,children:[w.jsxs("div",{className:Dt,children:[w.jsx("div",{className:Mt}),w.jsx("div",{className:Rt})]}),w.jsx("p",{className:Ht,children:"Loading..."})]}),Ft={notificationContainer:"_notificationContainer_yx8c7_1",notification:"_notification_yx8c7_1",slideIn:"_slideIn_yx8c7_1",success:"_success_yx8c7_25",error:"_error_yx8c7_29",warning:"_warning_yx8c7_33",info:"_info_yx8c7_37",icon:"_icon_yx8c7_41",content:"_content_yx8c7_62",closeButton:"_closeButton_yx8c7_68",slideOut:"_slideOut_yx8c7_1"},Vt=({id:e,type:t,message:n,duration:i})=>{const s=Nt((e=>e.removeNotification));a.useEffect((()=>{const t=setTimeout((()=>{s(e)}),i);return()=>clearTimeout(t)}),[e,i,s]);return w.jsxs("div",{className:`${Ft.notification} ${Ft[t]}`,children:[w.jsx("div",{className:Ft.icon,children:(()=>{switch(t){case"success":return w.jsx("i",{className:"fas fa-check-circle"});case"error":return w.jsx("i",{className:"fas fa-exclamation-circle"});case"warning":return w.jsx("i",{className:"fas fa-exclamation-triangle"});default:return w.jsx("i",{className:"fas fa-info-circle"})}})()}),w.jsx("div",{className:Ft.content,children:n}),w.jsx("button",{className:Ft.closeButton,onClick:()=>s(e),"aria-label":"Close notification",children:w.jsx("i",{className:"fas fa-times"})})]})},Bt=()=>{const e=Nt((e=>e.notifications));return 0===e.length?null:w.jsx("div",{className:Ft.notificationContainer,children:e.map((e=>w.jsx(Vt,{...e},e.id)))})},Ut="_backToTopButton_kvczo_1",zt="_buttonInner_kvczo_26",Gt="_arrowIcon_kvczo_38",Wt="_ringAnimation_kvczo_48",qt=()=>{const[e,t]=a.useState(!1);a.useEffect((()=>{const e=()=>{window.pageYOffset>300?t(!0):t(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)}),[]);return w.jsx(w.Fragment,{children:e&&w.jsxs("button",{className:Ut,onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Back to top",children:[w.jsx("div",{className:zt,children:w.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:Gt,children:w.jsx("path",{d:"M12 5L5 12M12 5L19 12M12 5V19",stroke:"white",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),w.jsx("div",{className:Wt})]})})},Yt="_cookieBanner_1ljvj_2",Zt="_bannerContent_1ljvj_27",Kt="_simpleView_1ljvj_34",Jt="_bannerText_1ljvj_41",Qt="_bannerTitle_1ljvj_46",Xt="_bannerDescription_1ljvj_54",en="_bannerSubtext_1ljvj_62",tn="_policyLink_1ljvj_69",nn="_bannerActions_1ljvj_83",sn="_customizeButton_1ljvj_89",on="_essentialButton_1ljvj_90",rn="_acceptButton_1ljvj_91",an="_detailsView_1ljvj_144",cn="_detailsHeader_1ljvj_149",ln="_detailsTitle_1ljvj_158",dn="_backButton_1ljvj_166",un="_cookieCategories_1ljvj_188",hn="_categorySection_1ljvj_192",pn="_categoryHeader_1ljvj_201",mn="_categoryInfo_1ljvj_211",fn="_categoryTitle_1ljvj_215",gn="_categoryDescription_1ljvj_222",_n="_toggleContainer_1ljvj_229",vn="_toggle_1ljvj_229",bn="_toggleLabel_1ljvj_248",yn="_cookieList_1ljvj_254",wn="_cookieItem_1ljvj_258",En="_cookieName_1ljvj_270",xn="_cookieDescription_1ljvj_277",Cn="_detailsActions_1ljvj_283",Sn="_saveButton_1ljvj_291",Tn="_acceptAllButton_1ljvj_292",An=()=>{const[e,t]=a.useState(!1),[n,i]=a.useState(!1),[s,o]=a.useState({[rt]:!0,[at]:!1,[ct]:!1,[lt]:!1});a.useEffect((()=>{const e=ut.getConsentPreferences();if(e.timestamp){const n=new Date(e.timestamp),i=new Date;i.setMonth(i.getMonth()-6),n<i?t(!0):o(e)}else t(!0);window.resetCookieConsent=()=>{ut.clearAllCookies(),document.cookie="researchsat_consent=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;",t(!0)},window.showCookieBanner=()=>{t(!0)}}),[]);const r=()=>{const e={[rt]:!0,[at]:!0,[ct]:!0,[lt]:!0};ut.setConsentPreferences(e),t(!1),ft.initializeTracking(),ft.trackEvent("consent_given",{type:"accept_all"})},c=(e,t)=>{o((n=>({...n,[e]:t})))},d=e=>Object.entries(dt).filter((([t,n])=>n.category===e)).map((([e,t])=>t));return e?w.jsx("div",{className:Yt,children:w.jsx("div",{className:Zt,children:n?w.jsxs("div",{className:an,children:[w.jsxs("div",{className:cn,children:[w.jsx("h3",{className:ln,children:"Cookie Preferences"}),w.jsx("button",{className:dn,onClick:()=>i(!1),children:"← Back"})]}),w.jsxs("div",{className:un,children:[w.jsxs("div",{className:hn,children:[w.jsxs("div",{className:pn,children:[w.jsxs("div",{className:mn,children:[w.jsx("h4",{className:fn,children:"Essential Cookies"}),w.jsx("p",{className:gn,children:"Required for basic website functionality. Cannot be disabled."})]}),w.jsxs("div",{className:_n,children:[w.jsx("input",{type:"checkbox",checked:!0,disabled:!0,className:vn}),w.jsx("span",{className:bn,children:"Always Active"})]})]}),w.jsx("div",{className:yn,children:d(rt).map((e=>w.jsxs("div",{className:wn,children:[w.jsx("span",{className:En,children:e.name}),w.jsx("span",{className:xn,children:e.description})]},e.name)))})]}),w.jsxs("div",{className:hn,children:[w.jsxs("div",{className:pn,children:[w.jsxs("div",{className:mn,children:[w.jsx("h4",{className:fn,children:"Analytics Cookies"}),w.jsx("p",{className:gn,children:"Help us understand how visitors interact with our website by collecting anonymous information."})]}),w.jsx("div",{className:_n,children:w.jsx("input",{type:"checkbox",checked:s[at],onChange:e=>c(at,e.target.checked),className:vn})})]}),w.jsx("div",{className:yn,children:d(at).map((e=>w.jsxs("div",{className:wn,children:[w.jsx("span",{className:En,children:e.name}),w.jsx("span",{className:xn,children:e.description})]},e.name)))})]}),w.jsxs("div",{className:hn,children:[w.jsxs("div",{className:pn,children:[w.jsxs("div",{className:mn,children:[w.jsx("h4",{className:fn,children:"Marketing Cookies"}),w.jsx("p",{className:gn,children:"Used to track visitors across websites to display relevant advertisements and measure campaign effectiveness."})]}),w.jsx("div",{className:_n,children:w.jsx("input",{type:"checkbox",checked:s[ct],onChange:e=>c(ct,e.target.checked),className:vn})})]}),w.jsx("div",{className:yn,children:d(ct).map((e=>w.jsxs("div",{className:wn,children:[w.jsx("span",{className:En,children:e.name}),w.jsx("span",{className:xn,children:e.description})]},e.name)))})]}),w.jsxs("div",{className:hn,children:[w.jsxs("div",{className:pn,children:[w.jsxs("div",{className:mn,children:[w.jsx("h4",{className:fn,children:"Personalization Cookies"}),w.jsx("p",{className:gn,children:"Remember your preferences and provide customized content and experiences."})]}),w.jsx("div",{className:_n,children:w.jsx("input",{type:"checkbox",checked:s[lt],onChange:e=>c(lt,e.target.checked),className:vn})})]}),w.jsx("div",{className:yn,children:d(lt).map((e=>w.jsxs("div",{className:wn,children:[w.jsx("span",{className:En,children:e.name}),w.jsx("span",{className:xn,children:e.description})]},e.name)))})]})]}),w.jsxs("div",{className:Cn,children:[w.jsx("button",{className:Sn,onClick:()=>{ut.setConsentPreferences(s),t(!1),s[at]&&ft.initializeTracking(),ft.trackEvent("consent_given",{type:"custom",preferences:s})},children:"Save Preferences"}),w.jsx("button",{className:Tn,onClick:r,children:"Accept All"})]})]}):w.jsxs("div",{className:Kt,children:[w.jsxs("div",{className:Jt,children:[w.jsx("h3",{className:Qt,children:"🍪 We use cookies to enhance your experience"}),w.jsx("p",{className:Xt,children:'We use cookies to analyze website traffic, personalize content, and provide social media features. By clicking "Accept All", you consent to our use of cookies.'}),w.jsxs("p",{className:en,children:[w.jsx(l,{to:"/privacy-policy",className:tn,children:"Privacy Policy"})," |",w.jsx(l,{to:"/terms-conditions",className:tn,children:"Cookie Policy"})]})]}),w.jsxs("div",{className:nn,children:[w.jsx("button",{className:sn,onClick:()=>i(!0),children:"Customize"}),w.jsx("button",{className:on,onClick:()=>{const e={[rt]:!0,[at]:!1,[ct]:!1,[lt]:!1};ut.setConsentPreferences(e),t(!1),ft.trackEvent("consent_given",{type:"essential_only"})},children:"Essential Only"}),w.jsx("button",{className:rn,onClick:r,children:"Accept All"})]})]})})}):null},On=a.createContext(),kn={notifications:!0,fontSize:"medium",animationsEnabled:!0,lastVisitedPage:"/",consentGiven:!1},jn=({children:e})=>{const[t,n]=a.useState((()=>{const e=localStorage.getItem("userPreferences");return e?JSON.parse(e):kn}));a.useEffect((()=>{localStorage.setItem("userPreferences",JSON.stringify(t))}),[t]);const i=(e,t)=>{e in kn&&n((n=>({...n,[e]:t})))},s={preferences:t,updatePreference:i,resetPreferences:()=>{n(kn)},updateLastVisitedPage:e=>{i("lastVisitedPage",e)},toggleNotifications:()=>{i("notifications",!t.notifications)},setFontSize:e=>{["small","medium","large"].includes(e)&&i("fontSize",e)},toggleAnimations:()=>{i("animationsEnabled",!t.animationsEnabled)},setConsent:e=>{i("consentGiven",e)}};return w.jsx(On.Provider,{value:s,children:e})},Nn=({children:e})=>w.jsx(jn,{children:e}),Ln={BOOKING_FOOTER_SCROLL:{name:"booking_footer_scroll",description:"User scrolls to footer on booking page without form interaction",threshold:85,cooldown:864e5,maxShows:1},EXIT_INTENT:{name:"exit_intent",description:"User moves cursor to close tab/window (DISABLED)",cooldown:864e5,maxShows:0},TIME_ON_SITE:{name:"time_on_site",description:"User spends significant time on site (DISABLED)",threshold:12e4,cooldown:432e5,maxShows:0},SCROLL_DEPTH:{name:"scroll_depth",description:"User scrolls deep into content (DISABLED)",threshold:75,cooldown:216e5,maxShows:0},FORM_ABANDONMENT:{name:"form_abandonment",description:"User starts but doesn't complete form (DISABLED)",cooldown:18e5,maxShows:0},RETURN_VISITOR:{name:"return_visitor",description:"User returns without converting (DISABLED)",threshold:2,cooldown:1728e5,maxShows:0},HIGH_VALUE_PAGE:{name:"high_value_page",description:"User visits high-value pages multiple times (DISABLED)",threshold:3,cooldown:864e5,maxShows:0}},In={type:"lead_capture",title:"Get Your Free Space Research Consultation",description:"Speak with our experts about your research goals",offer:"Free 30-minute consultation",cta:"Schedule Now",fields:["name","email","company","research_area"],incentive:"consultation"},Pn={type:"content_download",title:"Download Our Space Research Guide",description:"Comprehensive guide to microgravity research opportunities",offer:"Free Research Guide (PDF)",cta:"Download Now",fields:["name","email","company"],incentive:"content"},Dn={type:"demo_request",title:"See Our Platform in Action",description:"Get a personalized demo of our research capabilities",offer:"Live platform demo",cta:"Request Demo",fields:["name","email","company","phone"],incentive:"demo"};const Mn=new class{constructor(){this.activePopups=new Set,this.triggerHistory={},this.exitIntentListenerAdded=!1,this.scrollDepthTracked=!1,this.timeOnSiteTracked=!1,this.loadTriggerHistory(),this.initializeTracking()}initializeTracking(){ut.hasConsentForCategory(ct)&&this.isBookingPage()&&this.setupBookingPageTracking()}isBookingPage(){return"/book-mission"===window.location.pathname}setupBookingPageTracking(){this.userHasInteractedWithForm=!1,this.hasScrolledToFooter=!1,this.setupBookingFormTracking(),this.setupFooterScrollTracking()}setupBookingFormTracking(){const e=["footer",".footer",'[class*="footer"]',".newsletter",'[class*="newsletter"]',".retargeting-popup",'[class*="popup"]'],t=t=>{let n=t;for(;n&&n!==document.body;){for(const t of e)if(n.matches&&n.matches(t))return!0;n=n.parentElement}return!1},n=e=>{var i,s,o,r;const a=e.target;if(t(a))return;a.closest("form")&&(a.closest('[class*="booking"]')||a.closest('[class*="mission"]')||a.closest('[class*="contact"]')||(null==(i=a.name)?void 0:i.includes("booking"))||(null==(s=a.name)?void 0:s.includes("mission"))||(null==(o=a.placeholder)?void 0:o.toLowerCase().includes("mission"))||(null==(r=a.placeholder)?void 0:r.toLowerCase().includes("booking")))&&(this.userHasInteractedWithForm=!0,document.removeEventListener("focus",n,!0),document.removeEventListener("click",n,!0))};document.addEventListener("focus",n,!0),document.addEventListener("click",n,!0);new MutationObserver((e=>{e.forEach((e=>{e.addedNodes.forEach((e=>{if(1===e.nodeType&&!t(e)){(e.querySelectorAll?e.querySelectorAll("input, select, textarea, button"):[]).forEach((e=>{t(e)||(e.addEventListener("focus",n,{once:!0}),e.addEventListener("click",n,{once:!0}))}))}}))}))})).observe(document.body,{childList:!0,subtree:!0})}setupFooterScrollTracking(){if(this.scrollDepthTracked)return;const e=()=>{if(this.userHasInteractedWithForm)return void window.removeEventListener("scroll",e);const t=window.pageYOffset,n=document.documentElement.scrollHeight-window.innerHeight;Math.round(t/n*100)>=85&&!this.hasScrolledToFooter&&(this.hasScrolledToFooter=!0,this.triggerCampaign("BOOKING_FOOTER_SCROLL"),this.scrollDepthTracked=!0,window.removeEventListener("scroll",e))};window.addEventListener("scroll",e,{passive:!0})}setupFormAbandonmentTracking(){}triggerCampaign(e,t={}){if(!this.shouldShowCampaign(e))return!1;const n=this.selectCampaign(e,t);return!!n&&(this.showCampaign(n,e),this.recordTrigger(e),ft.trackEvent("retargeting_triggered",{triggerType:e,campaignType:n.type,...t}),!0)}shouldShowCampaign(e){const t=Ln[e];if(!t)return!1;const n=this.triggerHistory[e]||{count:0,lastShown:0};if(n.count>=t.maxShows)return!1;return!(Date.now()-n.lastShown<t.cooldown)&&!this.activePopups.has(e)}selectCampaign(e,t={}){const n=bt.getLeadSummary(),i=St.getPersonalizationData();return"HOT"===n.qualification||"WARM"===n.qualification||"FORM_ABANDONMENT"===e?this.customizeCampaign(In,i):"HIGH"===i.engagementLevel?this.customizeCampaign(Dn,i):(i.engagementLevel,this.customizeCampaign(Pn,i))}customizeCampaign(e,t){const n={...e};return"BIOTECH"===t.userSegment?(n.title=n.title.replace("Space Research","Biotech Research"),n.description=n.description.replace("research","biotech research")):"AEROSPACE"===t.userSegment&&(n.title=n.title.replace("Space Research","Aerospace Research"),n.description=n.description.replace("research","aerospace research")),"HIGH"===t.engagementLevel&&(n.urgency="Limited time offer"),n}showCampaign(e,t){const n=this.createPopupElement(e,t);document.body.appendChild(n),this.activePopups.add(t),setTimeout((()=>{this.closeCampaign(t)}),3e4)}createPopupElement(e,t){const n=document.createElement("div");return n.className="retargeting-popup",n.setAttribute("data-trigger",t),n.innerHTML=`\n      <div class="popup-overlay">\n        <div class="popup-content">\n          <button class="popup-close" onclick="window.retargetingEngine.closeCampaign('${t}')">&times;</button>\n          <div class="popup-header">\n            <div class="popup-icon">🚀</div>\n            <h3>${e.title}</h3>\n            ${e.urgency?`<div class="popup-urgency">${e.urgency}</div>`:""}\n          </div>\n          <div class="popup-body">\n            <p>${e.description}</p>\n            <div class="popup-offer">${e.offer}</div>\n            <form class="popup-form" onsubmit="window.retargetingEngine.handleFormSubmit(event, '${t}', '${e.type}')">\n              ${this.generateFormFields(e.fields)}\n              <button type="submit" class="popup-cta">${e.cta}</button>\n            </form>\n          </div>\n        </div>\n      </div>\n    `,this.addPopupStyles(n),n}generateFormFields(e){const t={name:'<input type="text" name="name" placeholder="Your Name" required>',email:'<input type="email" name="email" placeholder="Email Address" required>',company:'<input type="text" name="company" placeholder="Company Name">',phone:'<input type="tel" name="phone" placeholder="Phone Number">',research_area:'<select name="research_area"><option value="">Research Area</option><option value="biotech">Biotechnology</option><option value="pharma">Pharmaceutical</option><option value="materials">Materials Science</option><option value="other">Other</option></select>',project_type:'<select name="project_type"><option value="">Project Type</option><option value="payload">Payload Development</option><option value="mission">Full Mission</option><option value="consultation">Consultation</option></select>'};return e.map((e=>t[e]||"")).join("")}addPopupStyles(e){const t=document.createElement("style");t.textContent="\n      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');\n\n      .retargeting-popup {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        z-index: 10001;\n        animation: fadeIn 0.4s ease-out;\n        font-family: 'Poppins', sans-serif;\n      }\n\n      .popup-overlay {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.85);\n        backdrop-filter: blur(10px);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        padding: 20px;\n      }\n\n      .popup-content {\n        background: linear-gradient(135deg, #17242D 0%, #0E7369 100%);\n        border: 2px solid #0E7369;\n        border-radius: 20px;\n        max-width: 550px;\n        width: 100%;\n        position: relative;\n        animation: slideUp 0.4s ease-out;\n        box-shadow: 0 20px 40px rgba(14, 115, 105, 0.4);\n        overflow: hidden;\n      }\n      \n      .popup-close {\n        position: absolute;\n        top: 20px;\n        right: 25px;\n        background: rgba(255, 255, 255, 0.1);\n        border: 2px solid rgba(255, 255, 255, 0.3);\n        border-radius: 50%;\n        width: 40px;\n        height: 40px;\n        font-size: 20px;\n        cursor: pointer;\n        color: #ffffff;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.3s ease;\n        font-weight: bold;\n      }\n\n      .popup-close:hover {\n        background: rgba(255, 255, 255, 0.2);\n        border-color: rgba(255, 255, 255, 0.5);\n        transform: scale(1.1);\n      }\n\n      .popup-header {\n        padding: 40px 40px 25px;\n        text-align: center;\n        background: rgba(14, 115, 105, 0.1);\n        border-bottom: 1px solid rgba(14, 115, 105, 0.3);\n      }\n\n      .popup-icon {\n        font-size: 3rem;\n        margin-bottom: 15px;\n        filter: drop-shadow(0 0 10px rgba(14, 115, 105, 0.6));\n      }\n\n      .popup-header h3 {\n        margin: 0 0 15px;\n        color: #ffffff;\n        font-size: 1.8rem;\n        font-weight: 700;\n        line-height: 1.3;\n        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n      }\n\n      .popup-urgency {\n        color: #0E7369;\n        font-weight: 700;\n        font-size: 1rem;\n        text-transform: uppercase;\n        letter-spacing: 0.5px;\n        background: rgba(14, 115, 105, 0.2);\n        padding: 8px 16px;\n        border-radius: 20px;\n        display: inline-block;\n      }\n      \n      .popup-body {\n        padding: 0 40px 40px;\n      }\n\n      .popup-body p {\n        text-align: center;\n        color: rgba(255, 255, 255, 0.9);\n        margin-bottom: 25px;\n        font-size: 1.1rem;\n        line-height: 1.5;\n      }\n\n      .popup-offer {\n        background: rgba(14, 115, 105, 0.2);\n        border: 2px solid rgba(14, 115, 105, 0.4);\n        padding: 20px;\n        border-radius: 15px;\n        text-align: center;\n        font-weight: 700;\n        color: #0E7369;\n        margin-bottom: 25px;\n        font-size: 1.2rem;\n        text-transform: uppercase;\n        letter-spacing: 0.5px;\n        box-shadow: 0 0 20px rgba(14, 115, 105, 0.3);\n      }\n\n      .popup-form input,\n      .popup-form select {\n        width: 100%;\n        padding: 15px 20px;\n        margin-bottom: 20px;\n        border: 2px solid rgba(14, 115, 105, 0.3);\n        border-radius: 25px;\n        font-size: 1rem;\n        background: rgba(255, 255, 255, 0.1);\n        color: #ffffff;\n        font-family: 'Poppins', sans-serif;\n        transition: all 0.3s ease;\n        backdrop-filter: blur(10px);\n      }\n\n      .popup-form input::placeholder,\n      .popup-form select option {\n        color: rgba(255, 255, 255, 0.7);\n      }\n\n      .popup-form input:focus,\n      .popup-form select:focus {\n        outline: none;\n        border-color: #0E7369;\n        box-shadow: 0 0 0 3px rgba(14, 115, 105, 0.3);\n        background: rgba(255, 255, 255, 0.15);\n      }\n\n      .popup-cta {\n        width: 100%;\n        padding: 18px 30px;\n        background: #0E7369;\n        color: #ffffff;\n        border: 2px solid #0E7369;\n        border-radius: 25px;\n        font-size: 1.1rem;\n        font-weight: 700;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        text-transform: uppercase;\n        letter-spacing: 0.5px;\n        font-family: 'Poppins', sans-serif;\n        margin-top: 10px;\n      }\n\n      .popup-cta:hover {\n        background: transparent;\n        color: #0E7369;\n        transform: translateY(-2px);\n        box-shadow: 0 8px 25px rgba(14, 115, 105, 0.4);\n      }\n      \n      @keyframes fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n      }\n      \n      @keyframes slideUp {\n        from { transform: translateY(50px); opacity: 0; }\n        to { transform: translateY(0); opacity: 1; }\n      }\n    ",document.head.appendChild(t)}handleFormSubmit(e,t,n){e.preventDefault();const i=new FormData(e.target),s=Object.fromEntries(i.entries());ft.trackEvent("retargeting_conversion",{triggerType:t,campaignType:n,formData:s}),bt.updateCompanyProfile(s),this.closeCampaign(t),this.showSuccessMessage(n)}closeCampaign(e){const t=document.querySelector(`[data-trigger="${e}"]`);t&&t.remove(),this.activePopups.delete(e)}showSuccessMessage(e){}recordTrigger(e){this.triggerHistory[e]||(this.triggerHistory[e]={count:0,lastShown:0}),this.triggerHistory[e].count+=1,this.triggerHistory[e].lastShown=Date.now(),this.saveTriggerHistory()}loadTriggerHistory(){const e=ut.getCookie("RETARGETING_HISTORY");this.triggerHistory=e||{}}saveTriggerHistory(){ut.hasConsentForCategory(ct)&&ut.setCookie("RETARGETING_HISTORY",this.triggerHistory)}};window.retargetingEngine=Mn;const Rn=a.lazy((()=>S((()=>import("./HomePage-Ds1uLMfR.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12])))),Hn=a.lazy((()=>S((()=>import("./AboutPage-DWSHHSCD.js")),__vite__mapDeps([13,1,8,14,15,10,16,11,17])))),$n=a.lazy((()=>S((()=>import("./FeaturesPage-BeBAVLgH.js")),__vite__mapDeps([18,1,10,14,15,8,2,11,19])))),Fn=a.lazy((()=>S((()=>import("./PayloadsPage-BBEoP-IY.js")),__vite__mapDeps([20,1,10,8,7,9,21,11,22])))),Vn=a.lazy((()=>S((()=>import("./MissionsPage-C41dqNiZ.js")),__vite__mapDeps([23,1,10,24,14,15,25,8,26,7,9,21,11,27])))),Bn=a.lazy((()=>S((()=>import("./PastMissionsPage-mpWB073g.js")),__vite__mapDeps([28,1,10,14,15,25,8,26,7,9,11,29])))),Un=a.lazy((()=>S((()=>import("./CareersPage-DdtxmUAs.js")),__vite__mapDeps([30,1,10,11,31])))),zn=a.lazy((()=>S((()=>import("./ContactPage-DGlo0kB6.js")),__vite__mapDeps([32,1,8,10,11,33])))),Gn=a.lazy((()=>S((()=>import("./PartnershipsPage-CFLIVn2k.js")),__vite__mapDeps([34,1,10,14,15,3,4,11,35])))),Wn=a.lazy((()=>S((()=>import("./NewsPage-ClWpG3d_.js")),__vite__mapDeps([36,1,10,14,15,5,6,11,37])))),qn=a.lazy((()=>S((()=>import("./NewsArticlePage--wX97tYz.js")),__vite__mapDeps([38,1,10,14,15,5,11,39])))),Yn=a.lazy((()=>S((()=>import("./PrivacyPolicyPage-B7XZKw5y.js")),__vite__mapDeps([40,1,11])))),Zn=a.lazy((()=>S((()=>import("./TermsConditionsPage-BKhpc_t5.js")),__vite__mapDeps([41,1,11])))),Kn=a.lazy((()=>S((()=>import("./EmailSignPage-QmWCGt5n.js")),__vite__mapDeps([42,1,10,11])))),Jn=a.lazy((()=>S((()=>import("./TestPage-CAcUZ-sy.js")),__vite__mapDeps([43,1,11])))),Qn=a.lazy((()=>S((()=>import("./BookMissionPage-CVDRRER-.js")),__vite__mapDeps([44,1,10,45,24,11,46])))),Xn=a.lazy((()=>S((()=>import("./SpaceXperimentPage-DziACns4.js")),__vite__mapDeps([47,1,45,11,48])))),ei=a.lazy((()=>S((()=>import("./OfferingsPage-8Y6ffZ-f.js")),__vite__mapDeps([49,1,16,11,50])))),ti=a.lazy((()=>S((()=>import("./GalleryPage-Cmi_ndwY.js")),__vite__mapDeps([51,1,10,11,52])))),ni=a.lazy((()=>S((()=>import("./MicrogravityGuide-DRUgQck6.js")),__vite__mapDeps([53,1,11,54])))),ii=a.lazy((()=>S((()=>import("./NotFoundPage-BF3u9nrX.js")),__vite__mapDeps([55,1,11])))),si=()=>{const e=u(),t=Nt((e=>e.trackPageView));return a.useEffect((()=>{t(e.pathname),ft.trackPageView(e.pathname,{title:document.title,url:window.location.href}),bt.calculateLeadScore()}),[e.pathname,t]),w.jsxs(w.Fragment,{children:[w.jsx(Te,{}),w.jsx(a.Suspense,{fallback:w.jsx($t,{}),children:w.jsxs(h,{children:[w.jsx(p,{path:"/",element:w.jsx(Rn,{})}),w.jsx(p,{path:"/about",element:w.jsx(Hn,{})}),w.jsx(p,{path:"/features",element:w.jsx($n,{})}),w.jsx(p,{path:"/payloads",element:w.jsx(Fn,{})}),w.jsx(p,{path:"/missions",element:w.jsx(Vn,{})}),w.jsx(p,{path:"/past-missions",element:w.jsx(Bn,{})}),w.jsx(p,{path:"/careers",element:w.jsx(Un,{})}),w.jsx(p,{path:"/contact",element:w.jsx(zn,{})}),w.jsx(p,{path:"/partnerships",element:w.jsx(Gn,{})}),w.jsx(p,{path:"/news",element:w.jsx(Wn,{})}),w.jsx(p,{path:"/news/article/:id",element:w.jsx(qn,{})}),w.jsx(p,{path:"/gallery",element:w.jsx(ti,{})}),w.jsx(p,{path:"/privacy-policy",element:w.jsx(Yn,{})}),w.jsx(p,{path:"/terms-conditions",element:w.jsx(Zn,{})}),w.jsx(p,{path:"/emailSign",element:w.jsx(Kn,{})}),w.jsx(p,{path:"/test",element:w.jsx(Jn,{})}),w.jsx(p,{path:"/book-mission",element:w.jsx(Qn,{})}),w.jsx(p,{path:"/spacexperiment",element:w.jsx(Xn,{})}),w.jsx(p,{path:"/offerings",element:w.jsx(ei,{})}),w.jsx(p,{path:"/microgravity-guide",element:w.jsx(ni,{})}),w.jsx(p,{path:"*",element:w.jsx(ii,{})})]})}),w.jsx(It,{}),w.jsx(qt,{})]})},oi=()=>w.jsx(ge,{children:w.jsx(Nn,{children:w.jsxs(d,{children:[w.jsx(si,{}),w.jsx(Bt,{}),w.jsx(An,{})]})})});var ri,ai={exports:{}};
/*!
  * Bootstrap v5.3.6 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */ri||(ri=1,ai.exports=function(){const e=new Map,t={set(t,n,i){e.has(t)||e.set(t,new Map);const s=e.get(t);(s.has(n)||0===s.size)&&s.set(n,i)},get:(t,n)=>e.has(t)&&e.get(t).get(n)||null,remove(t,n){if(!e.has(t))return;const i=e.get(t);i.delete(n),0===i.size&&e.delete(t)}},n="transitionend",i=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,((e,t)=>`#${CSS.escape(t)}`))),e),s=e=>{e.dispatchEvent(new Event(n))},o=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),r=e=>o(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(i(e)):null,a=e=>{if(!o(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},c=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),l=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?l(e.parentNode):null},d=()=>{},u=e=>{e.offsetHeight},h=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,p=[],m=()=>"rtl"===document.documentElement.dir,f=e=>{var t;t=()=>{const t=h();if(t){const n=e.NAME,i=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=i,e.jQueryInterface)}},"loading"===document.readyState?(p.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of p)e()})),p.push(t)):t()},g=(e,t=[],n=e)=>"function"==typeof e?e.call(...t):n,_=(e,t,i=!0)=>{if(!i)return void g(e);const o=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const i=Number.parseFloat(t),s=Number.parseFloat(n);return i||s?(t=t.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(n))):0})(t)+5;let r=!1;const a=({target:i})=>{i===t&&(r=!0,t.removeEventListener(n,a),g(e))};t.addEventListener(n,a),setTimeout((()=>{r||s(t)}),o)},v=(e,t,n,i)=>{const s=e.length;let o=e.indexOf(t);return-1===o?!n&&i?e[s-1]:e[0]:(o+=n?1:-1,i&&(o=(o+s)%s),e[Math.max(0,Math.min(o,s-1))])},b=/[^.]*(?=\..*)\.|.*/,y=/\..*/,w=/::\d+$/,E={};let x=1;const C={mouseenter:"mouseover",mouseleave:"mouseout"},S=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function T(e,t){return t&&`${t}::${x++}`||e.uidEvent||x++}function A(e){const t=T(e);return e.uidEvent=t,E[t]=E[t]||{},E[t]}function O(e,t,n=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===n))}function k(e,t,n){const i="string"==typeof t,s=i?n:t||n;let o=I(e);return S.has(o)||(o=e),[i,s,o]}function j(e,t,n,i,s){if("string"!=typeof t||!e)return;let[o,r,a]=k(t,n,i);var c;t in C&&(c=r,r=function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return c.call(this,e)});const l=A(e),d=l[a]||(l[a]={}),u=O(d,r,o?n:null);if(u)return void(u.oneOff=u.oneOff&&s);const h=T(r,t.replace(b,"")),p=o?function(e,t,n){return function i(s){const o=e.querySelectorAll(t);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return D(s,{delegateTarget:r}),i.oneOff&&P.off(e,s.type,t,n),n.apply(r,[s])}}(e,n,r):function(e,t){return function n(i){return D(i,{delegateTarget:e}),n.oneOff&&P.off(e,i.type,t),t.apply(e,[i])}}(e,r);p.delegationSelector=o?n:null,p.callable=r,p.oneOff=s,p.uidEvent=h,d[h]=p,e.addEventListener(a,p,o)}function N(e,t,n,i,s){const o=O(t[n],i,s);o&&(e.removeEventListener(n,o,Boolean(s)),delete t[n][o.uidEvent])}function L(e,t,n,i){const s=t[n]||{};for(const[o,r]of Object.entries(s))o.includes(i)&&N(e,t,n,r.callable,r.delegationSelector)}function I(e){return e=e.replace(y,""),C[e]||e}const P={on(e,t,n,i){j(e,t,n,i,!1)},one(e,t,n,i){j(e,t,n,i,!0)},off(e,t,n,i){if("string"!=typeof t||!e)return;const[s,o,r]=k(t,n,i),a=r!==t,c=A(e),l=c[r]||{},d=t.startsWith(".");if(void 0===o){if(d)for(const n of Object.keys(c))L(e,c,n,t.slice(1));for(const[n,i]of Object.entries(l)){const s=n.replace(w,"");a&&!t.includes(s)||N(e,c,r,i.callable,i.delegationSelector)}}else{if(!Object.keys(l).length)return;N(e,c,r,o,s?n:null)}},trigger(e,t,n){if("string"!=typeof t||!e)return null;const i=h();let s=null,o=!0,r=!0,a=!1;t!==I(t)&&i&&(s=i.Event(t,n),i(e).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const c=D(new Event(t,{bubbles:o,cancelable:!0}),n);return a&&c.preventDefault(),r&&e.dispatchEvent(c),c.defaultPrevented&&s&&s.preventDefault(),c}};function D(e,t={}){for(const[i,s]of Object.entries(t))try{e[i]=s}catch(n){Object.defineProperty(e,i,{configurable:!0,get:()=>s})}return e}function M(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function R(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}const H={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${R(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${R(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const i of n){let n=i.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1),t[n]=M(e.dataset[i])}return t},getDataAttribute:(e,t)=>M(e.getAttribute(`data-bs-${R(t)}`))};class ${static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const n=o(t)?H.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...o(t)?H.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[i,s]of Object.entries(t)){const t=e[i],r=o(t)?"element":null==(n=t)?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${r}" but expected type "${s}".`)}var n}}class F extends ${constructor(e,n){super(),(e=r(e))&&(this._element=e,this._config=this._getConfig(n),t.set(this._element,this.constructor.DATA_KEY,this))}dispose(){t.remove(this._element,this.constructor.DATA_KEY),P.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){_(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return t.get(r(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.6"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const V=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&"#"!==n?n.trim():null}return t?t.split(",").map((e=>i(e))).join(","):null},B={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const n=[];let i=e.parentNode.closest(t);for(;i;)n.push(i),i=i.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(t,e).filter((e=>!c(e)&&a(e)))},getSelectorFromElement(e){const t=V(e);return t&&B.findOne(t)?t:null},getElementFromSelector(e){const t=V(e);return t?B.findOne(t):null},getMultipleElementsFromSelector(e){const t=V(e);return t?B.find(t):[]}},U=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,i=e.NAME;P.on(document,n,`[data-bs-dismiss="${i}"]`,(function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),c(this))return;const s=B.getElementFromSelector(this)||this.closest(`.${i}`);e.getOrCreateInstance(s)[t]()}))},z=".bs.alert",G=`close${z}`,W=`closed${z}`;class q extends F{static get NAME(){return"alert"}close(){if(P.trigger(this._element,G).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,e)}_destroyElement(){this._element.remove(),P.trigger(this._element,W),this.dispose()}static jQueryInterface(e){return this.each((function(){const t=q.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}U(q,"close"),f(q);const Y='[data-bs-toggle="button"]';class Z extends F{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each((function(){const t=Z.getOrCreateInstance(this);"toggle"===e&&t[e]()}))}}P.on(document,"click.bs.button.data-api",Y,(e=>{e.preventDefault();const t=e.target.closest(Y);Z.getOrCreateInstance(t).toggle()})),f(Z);const K=".bs.swipe",J=`touchstart${K}`,Q=`touchmove${K}`,X=`touchend${K}`,ee=`pointerdown${K}`,te=`pointerup${K}`,ne={endCallback:null,leftCallback:null,rightCallback:null},ie={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class se extends ${constructor(e,t){super(),this._element=e,e&&se.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return ne}static get DefaultType(){return ie}static get NAME(){return"swipe"}dispose(){P.off(this._element,K)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),g(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&g(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(P.on(this._element,ee,(e=>this._start(e))),P.on(this._element,te,(e=>this._end(e))),this._element.classList.add("pointer-event")):(P.on(this._element,J,(e=>this._start(e))),P.on(this._element,Q,(e=>this._move(e))),P.on(this._element,X,(e=>this._end(e))))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const oe=".bs.carousel",re=".data-api",ae="ArrowLeft",ce="ArrowRight",le="next",de="prev",ue="left",he="right",pe=`slide${oe}`,me=`slid${oe}`,fe=`keydown${oe}`,ge=`mouseenter${oe}`,_e=`mouseleave${oe}`,ve=`dragstart${oe}`,be=`load${oe}${re}`,ye=`click${oe}${re}`,we="carousel",Ee="active",xe=".active",Ce=".carousel-item",Se=xe+Ce,Te={[ae]:he,[ce]:ue},Ae={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Oe={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class ke extends F{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=B.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===we&&this.cycle()}static get Default(){return Ae}static get DefaultType(){return Oe}static get NAME(){return"carousel"}next(){this._slide(le)}nextWhenVisible(){!document.hidden&&a(this._element)&&this.next()}prev(){this._slide(de)}pause(){this._isSliding&&s(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?P.one(this._element,me,(()=>this.cycle())):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void P.one(this._element,me,(()=>this.to(e)));const n=this._getItemIndex(this._getActive());if(n===e)return;const i=e>n?le:de;this._slide(i,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&P.on(this._element,fe,(e=>this._keydown(e))),"hover"===this._config.pause&&(P.on(this._element,ge,(()=>this.pause())),P.on(this._element,_e,(()=>this._maybeEnableCycle()))),this._config.touch&&se.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of B.find(".carousel-item img",this._element))P.on(t,ve,(e=>e.preventDefault()));const e={leftCallback:()=>this._slide(this._directionToOrder(ue)),rightCallback:()=>this._slide(this._directionToOrder(he)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new se(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=Te[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=B.findOne(xe,this._indicatorsElement);t.classList.remove(Ee),t.removeAttribute("aria-current");const n=B.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);n&&(n.classList.add(Ee),n.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const n=this._getActive(),i=e===le,s=t||v(this._getItems(),n,i,this._config.wrap);if(s===n)return;const o=this._getItemIndex(s),r=t=>P.trigger(this._element,t,{relatedTarget:s,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:o});if(r(pe).defaultPrevented)return;if(!n||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const c=i?"carousel-item-start":"carousel-item-end",l=i?"carousel-item-next":"carousel-item-prev";s.classList.add(l),u(s),n.classList.add(c),s.classList.add(c),this._queueCallback((()=>{s.classList.remove(c,l),s.classList.add(Ee),n.classList.remove(Ee,l,c),this._isSliding=!1,r(me)}),n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return B.findOne(Se,this._element)}_getItems(){return B.find(Ce,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return m()?e===ue?de:le:e===ue?le:de}_orderToDirection(e){return m()?e===de?ue:he:e===de?he:ue}static jQueryInterface(e){return this.each((function(){const t=ke.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)}))}}P.on(document,ye,"[data-bs-slide], [data-bs-slide-to]",(function(e){const t=B.getElementFromSelector(this);if(!t||!t.classList.contains(we))return;e.preventDefault();const n=ke.getOrCreateInstance(t),i=this.getAttribute("data-bs-slide-to");return i?(n.to(i),void n._maybeEnableCycle()):"next"===H.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())})),P.on(window,be,(()=>{const e=B.find('[data-bs-ride="carousel"]');for(const t of e)ke.getOrCreateInstance(t)})),f(ke);const je=".bs.collapse",Ne=`show${je}`,Le=`shown${je}`,Ie=`hide${je}`,Pe=`hidden${je}`,De=`click${je}.data-api`,Me="show",Re="collapse",He="collapsing",$e=`:scope .${Re} .${Re}`,Fe='[data-bs-toggle="collapse"]',Ve={parent:null,toggle:!0},Be={parent:"(null|element)",toggle:"boolean"};class Ue extends F{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const n=B.find(Fe);for(const i of n){const e=B.getSelectorFromElement(i),t=B.find(e).filter((e=>e===this._element));null!==e&&t.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Ve}static get DefaultType(){return Be}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((e=>e!==this._element)).map((e=>Ue.getOrCreateInstance(e,{toggle:!1})))),e.length&&e[0]._isTransitioning)return;if(P.trigger(this._element,Ne).defaultPrevented)return;for(const i of e)i.hide();const t=this._getDimension();this._element.classList.remove(Re),this._element.classList.add(He),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(He),this._element.classList.add(Re,Me),this._element.style[t]="",P.trigger(this._element,Le)}),this._element,!0),this._element.style[t]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(P.trigger(this._element,Ie).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,u(this._element),this._element.classList.add(He),this._element.classList.remove(Re,Me);for(const t of this._triggerArray){const e=B.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(He),this._element.classList.add(Re),P.trigger(this._element,Pe)}),this._element,!0)}_isShown(e=this._element){return e.classList.contains(Me)}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=r(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(Fe);for(const t of e){const e=B.getElementFromSelector(t);e&&this._addAriaAndCollapsedClass([t],this._isShown(e))}}_getFirstLevelChildren(e){const t=B.find($e,this._config.parent);return B.find(e,this._config.parent).filter((e=>!t.includes(e)))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each((function(){const n=Ue.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e]()}}))}}P.on(document,De,Fe,(function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();for(const t of B.getMultipleElementsFromSelector(this))Ue.getOrCreateInstance(t,{toggle:!1}).toggle()})),f(Ue);var ze="top",Ge="bottom",We="right",qe="left",Ye="auto",Ze=[ze,Ge,We,qe],Ke="start",Je="end",Qe="clippingParents",Xe="viewport",et="popper",tt="reference",nt=Ze.reduce((function(e,t){return e.concat([t+"-"+Ke,t+"-"+Je])}),[]),it=[].concat(Ze,[Ye]).reduce((function(e,t){return e.concat([t,t+"-"+Ke,t+"-"+Je])}),[]),st="beforeRead",ot="read",rt="afterRead",at="beforeMain",ct="main",lt="afterMain",dt="beforeWrite",ut="write",ht="afterWrite",pt=[st,ot,rt,at,ct,lt,dt,ut,ht];function mt(e){return e?(e.nodeName||"").toLowerCase():null}function ft(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function gt(e){return e instanceof ft(e).Element||e instanceof Element}function _t(e){return e instanceof ft(e).HTMLElement||e instanceof HTMLElement}function vt(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ft(e).ShadowRoot||e instanceof ShadowRoot)}const bt={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},i=t.attributes[e]||{},s=t.elements[e];_t(s)&&mt(s)&&(Object.assign(s.style,n),Object.keys(i).forEach((function(e){var t=i[e];!1===t?s.removeAttribute(e):s.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var i=t.elements[e],s=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});_t(i)&&mt(i)&&(Object.assign(i.style,o),Object.keys(s).forEach((function(e){i.removeAttribute(e)})))}))}},requires:["computeStyles"]};function yt(e){return e.split("-")[0]}var wt=Math.max,Et=Math.min,xt=Math.round;function Ct(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function St(){return!/^((?!chrome|android).)*safari/i.test(Ct())}function Tt(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var i=e.getBoundingClientRect(),s=1,o=1;t&&_t(e)&&(s=e.offsetWidth>0&&xt(i.width)/e.offsetWidth||1,o=e.offsetHeight>0&&xt(i.height)/e.offsetHeight||1);var r=(gt(e)?ft(e):window).visualViewport,a=!St()&&n,c=(i.left+(a&&r?r.offsetLeft:0))/s,l=(i.top+(a&&r?r.offsetTop:0))/o,d=i.width/s,u=i.height/o;return{width:d,height:u,top:l,right:c+d,bottom:l+u,left:c,x:c,y:l}}function At(e){var t=Tt(e),n=e.offsetWidth,i=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-i)<=1&&(i=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:i}}function Ot(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&vt(n)){var i=t;do{if(i&&e.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function kt(e){return ft(e).getComputedStyle(e)}function jt(e){return["table","td","th"].indexOf(mt(e))>=0}function Nt(e){return((gt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Lt(e){return"html"===mt(e)?e:e.assignedSlot||e.parentNode||(vt(e)?e.host:null)||Nt(e)}function It(e){return _t(e)&&"fixed"!==kt(e).position?e.offsetParent:null}function Pt(e){for(var t=ft(e),n=It(e);n&&jt(n)&&"static"===kt(n).position;)n=It(n);return n&&("html"===mt(n)||"body"===mt(n)&&"static"===kt(n).position)?t:n||function(e){var t=/firefox/i.test(Ct());if(/Trident/i.test(Ct())&&_t(e)&&"fixed"===kt(e).position)return null;var n=Lt(e);for(vt(n)&&(n=n.host);_t(n)&&["html","body"].indexOf(mt(n))<0;){var i=kt(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||t&&"filter"===i.willChange||t&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(e)||t}function Dt(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Mt(e,t,n){return wt(e,Et(t,n))}function Rt(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Ht(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}const $t={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n,i,s=e.state,o=e.name,r=e.options,a=s.elements.arrow,c=s.modifiersData.popperOffsets,l=yt(s.placement),d=Dt(l),u=[qe,We].indexOf(l)>=0?"height":"width";if(a&&c){var h=(n=r.padding,i=s,Rt("number"!=typeof(n="function"==typeof n?n(Object.assign({},i.rects,{placement:i.placement})):n)?n:Ht(n,Ze))),p=At(a),m="y"===d?ze:qe,f="y"===d?Ge:We,g=s.rects.reference[u]+s.rects.reference[d]-c[d]-s.rects.popper[u],_=c[d]-s.rects.reference[d],v=Pt(a),b=v?"y"===d?v.clientHeight||0:v.clientWidth||0:0,y=g/2-_/2,w=h[m],E=b-p[u]-h[f],x=b/2-p[u]/2+y,C=Mt(w,x,E),S=d;s.modifiersData[o]=((t={})[S]=C,t.centerOffset=C-x,t)}},effect:function(e){var t=e.state,n=e.options.element,i=void 0===n?"[data-popper-arrow]":n;null!=i&&("string"!=typeof i||(i=t.elements.popper.querySelector(i)))&&Ot(t.elements.popper,i)&&(t.elements.arrow=i)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ft(e){return e.split("-")[1]}var Vt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Bt(e){var t,n=e.popper,i=e.popperRect,s=e.placement,o=e.variation,r=e.offsets,a=e.position,c=e.gpuAcceleration,l=e.adaptive,d=e.roundOffsets,u=e.isFixed,h=r.x,p=void 0===h?0:h,m=r.y,f=void 0===m?0:m,g="function"==typeof d?d({x:p,y:f}):{x:p,y:f};p=g.x,f=g.y;var _=r.hasOwnProperty("x"),v=r.hasOwnProperty("y"),b=qe,y=ze,w=window;if(l){var E=Pt(n),x="clientHeight",C="clientWidth";E===ft(n)&&"static"!==kt(E=Nt(n)).position&&"absolute"===a&&(x="scrollHeight",C="scrollWidth"),(s===ze||(s===qe||s===We)&&o===Je)&&(y=Ge,f-=(u&&E===w&&w.visualViewport?w.visualViewport.height:E[x])-i.height,f*=c?1:-1),s!==qe&&(s!==ze&&s!==Ge||o!==Je)||(b=We,p-=(u&&E===w&&w.visualViewport?w.visualViewport.width:E[C])-i.width,p*=c?1:-1)}var S,T,A,O,k,j,N=Object.assign({position:a},l&&Vt),L=!0===d?(T={x:p,y:f},A=ft(n),O=T.x,k=T.y,j=A.devicePixelRatio||1,{x:xt(O*j)/j||0,y:xt(k*j)/j||0}):{x:p,y:f};return p=L.x,f=L.y,c?Object.assign({},N,((S={})[y]=v?"0":"",S[b]=_?"0":"",S.transform=(w.devicePixelRatio||1)<=1?"translate("+p+"px, "+f+"px)":"translate3d("+p+"px, "+f+"px, 0)",S)):Object.assign({},N,((t={})[y]=v?f+"px":"",t[b]=_?p+"px":"",t.transform="",t))}const Ut={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,i=n.gpuAcceleration,s=void 0===i||i,o=n.adaptive,r=void 0===o||o,a=n.roundOffsets,c=void 0===a||a,l={placement:yt(t.placement),variation:Ft(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Bt(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:r,roundOffsets:c})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Bt(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var zt={passive:!0};const Gt={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,i=e.options,s=i.scroll,o=void 0===s||s,r=i.resize,a=void 0===r||r,c=ft(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&l.forEach((function(e){e.addEventListener("scroll",n.update,zt)})),a&&c.addEventListener("resize",n.update,zt),function(){o&&l.forEach((function(e){e.removeEventListener("scroll",n.update,zt)})),a&&c.removeEventListener("resize",n.update,zt)}},data:{}};var Wt={left:"right",right:"left",bottom:"top",top:"bottom"};function qt(e){return e.replace(/left|right|bottom|top/g,(function(e){return Wt[e]}))}var Yt={start:"end",end:"start"};function Zt(e){return e.replace(/start|end/g,(function(e){return Yt[e]}))}function Kt(e){var t=ft(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function Jt(e){return Tt(Nt(e)).left+Kt(e).scrollLeft}function Qt(e){var t=kt(e),n=t.overflow,i=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+i)}function Xt(e){return["html","body","#document"].indexOf(mt(e))>=0?e.ownerDocument.body:_t(e)&&Qt(e)?e:Xt(Lt(e))}function en(e,t){var n;void 0===t&&(t=[]);var i=Xt(e),s=i===(null==(n=e.ownerDocument)?void 0:n.body),o=ft(i),r=s?[o].concat(o.visualViewport||[],Qt(i)?i:[]):i,a=t.concat(r);return s?a:a.concat(en(Lt(r)))}function tn(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function nn(e,t,n){return t===Xe?tn(function(e,t){var n=ft(e),i=Nt(e),s=n.visualViewport,o=i.clientWidth,r=i.clientHeight,a=0,c=0;if(s){o=s.width,r=s.height;var l=St();(l||!l&&"fixed"===t)&&(a=s.offsetLeft,c=s.offsetTop)}return{width:o,height:r,x:a+Jt(e),y:c}}(e,n)):gt(t)?((s=Tt(i=t,!1,"fixed"===n)).top=s.top+i.clientTop,s.left=s.left+i.clientLeft,s.bottom=s.top+i.clientHeight,s.right=s.left+i.clientWidth,s.width=i.clientWidth,s.height=i.clientHeight,s.x=s.left,s.y=s.top,s):tn(function(e){var t,n=Nt(e),i=Kt(e),s=null==(t=e.ownerDocument)?void 0:t.body,o=wt(n.scrollWidth,n.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=wt(n.scrollHeight,n.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-i.scrollLeft+Jt(e),c=-i.scrollTop;return"rtl"===kt(s||n).direction&&(a+=wt(n.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:c}}(Nt(e)));var i,s}function sn(e){var t,n=e.reference,i=e.element,s=e.placement,o=s?yt(s):null,r=s?Ft(s):null,a=n.x+n.width/2-i.width/2,c=n.y+n.height/2-i.height/2;switch(o){case ze:t={x:a,y:n.y-i.height};break;case Ge:t={x:a,y:n.y+n.height};break;case We:t={x:n.x+n.width,y:c};break;case qe:t={x:n.x-i.width,y:c};break;default:t={x:n.x,y:n.y}}var l=o?Dt(o):null;if(null!=l){var d="y"===l?"height":"width";switch(r){case Ke:t[l]=t[l]-(n[d]/2-i[d]/2);break;case Je:t[l]=t[l]+(n[d]/2-i[d]/2)}}return t}function on(e,t){void 0===t&&(t={});var n,i,s,o,r,a,c,l,d,u,h,p=t,m=p.placement,f=void 0===m?e.placement:m,g=p.strategy,_=void 0===g?e.strategy:g,v=p.boundary,b=void 0===v?Qe:v,y=p.rootBoundary,w=void 0===y?Xe:y,E=p.elementContext,x=void 0===E?et:E,C=p.altBoundary,S=void 0!==C&&C,T=p.padding,A=void 0===T?0:T,O=Rt("number"!=typeof A?A:Ht(A,Ze)),k=x===et?tt:et,j=e.rects.popper,N=e.elements[S?k:x],L=(n=gt(N)?N:N.contextElement||Nt(e.elements.popper),s=w,o=_,l="clippingParents"===(i=b)?(a=en(Lt(r=n)),gt(c=["absolute","fixed"].indexOf(kt(r).position)>=0&&_t(r)?Pt(r):r)?a.filter((function(e){return gt(e)&&Ot(e,c)&&"body"!==mt(e)})):[]):[].concat(i),d=[].concat(l,[s]),u=d[0],h=d.reduce((function(e,t){var i=nn(n,t,o);return e.top=wt(i.top,e.top),e.right=Et(i.right,e.right),e.bottom=Et(i.bottom,e.bottom),e.left=wt(i.left,e.left),e}),nn(n,u,o)),h.width=h.right-h.left,h.height=h.bottom-h.top,h.x=h.left,h.y=h.top,h),I=Tt(e.elements.reference),P=sn({reference:I,element:j,placement:f}),D=tn(Object.assign({},j,P)),M=x===et?D:I,R={top:L.top-M.top+O.top,bottom:M.bottom-L.bottom+O.bottom,left:L.left-M.left+O.left,right:M.right-L.right+O.right},H=e.modifiersData.offset;if(x===et&&H){var $=H[f];Object.keys(R).forEach((function(e){var t=[We,Ge].indexOf(e)>=0?1:-1,n=[ze,Ge].indexOf(e)>=0?"y":"x";R[e]+=$[n]*t}))}return R}function rn(e,t){void 0===t&&(t={});var n=t,i=n.placement,s=n.boundary,o=n.rootBoundary,r=n.padding,a=n.flipVariations,c=n.allowedAutoPlacements,l=void 0===c?it:c,d=Ft(i),u=d?a?nt:nt.filter((function(e){return Ft(e)===d})):Ze,h=u.filter((function(e){return l.indexOf(e)>=0}));0===h.length&&(h=u);var p=h.reduce((function(t,n){return t[n]=on(e,{placement:n,boundary:s,rootBoundary:o,padding:r})[yt(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}const an={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,i=e.name;if(!t.modifiersData[i]._skip){for(var s=n.mainAxis,o=void 0===s||s,r=n.altAxis,a=void 0===r||r,c=n.fallbackPlacements,l=n.padding,d=n.boundary,u=n.rootBoundary,h=n.altBoundary,p=n.flipVariations,m=void 0===p||p,f=n.allowedAutoPlacements,g=t.options.placement,_=yt(g),v=c||(_!==g&&m?function(e){if(yt(e)===Ye)return[];var t=qt(e);return[Zt(e),t,Zt(t)]}(g):[qt(g)]),b=[g].concat(v).reduce((function(e,n){return e.concat(yt(n)===Ye?rn(t,{placement:n,boundary:d,rootBoundary:u,padding:l,flipVariations:m,allowedAutoPlacements:f}):n)}),[]),y=t.rects.reference,w=t.rects.popper,E=new Map,x=!0,C=b[0],S=0;S<b.length;S++){var T=b[S],A=yt(T),O=Ft(T)===Ke,k=[ze,Ge].indexOf(A)>=0,j=k?"width":"height",N=on(t,{placement:T,boundary:d,rootBoundary:u,altBoundary:h,padding:l}),L=k?O?We:qe:O?Ge:ze;y[j]>w[j]&&(L=qt(L));var I=qt(L),P=[];if(o&&P.push(N[A]<=0),a&&P.push(N[L]<=0,N[I]<=0),P.every((function(e){return e}))){C=T,x=!1;break}E.set(T,P)}if(x)for(var D=function(e){var t=b.find((function(t){var n=E.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return C=t,"break"},M=m?3:1;M>0&&"break"!==D(M);M--);t.placement!==C&&(t.modifiersData[i]._skip=!0,t.placement=C,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function cn(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function ln(e){return[ze,We,Ge,qe].some((function(t){return e[t]>=0}))}const dn={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,i=t.rects.reference,s=t.rects.popper,o=t.modifiersData.preventOverflow,r=on(t,{elementContext:"reference"}),a=on(t,{altBoundary:!0}),c=cn(r,i),l=cn(a,s,o),d=ln(c),u=ln(l);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:l,isReferenceHidden:d,hasPopperEscaped:u},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":u})}},un={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,i=e.name,s=n.offset,o=void 0===s?[0,0]:s,r=it.reduce((function(e,n){return e[n]=(i=n,s=t.rects,r=o,a=yt(i),c=[qe,ze].indexOf(a)>=0?-1:1,l="function"==typeof r?r(Object.assign({},s,{placement:i})):r,d=l[0],u=l[1],d=d||0,u=(u||0)*c,[qe,We].indexOf(a)>=0?{x:u,y:d}:{x:d,y:u}),e;var i,s,r,a,c,l,d,u}),{}),a=r[t.placement],c=a.x,l=a.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=l),t.modifiersData[i]=r}},hn={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=sn({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},pn={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t,n,i=e.state,s=e.options,o=e.name,r=s.mainAxis,a=void 0===r||r,c=s.altAxis,l=void 0!==c&&c,d=s.boundary,u=s.rootBoundary,h=s.altBoundary,p=s.padding,m=s.tether,f=void 0===m||m,g=s.tetherOffset,_=void 0===g?0:g,v=on(i,{boundary:d,rootBoundary:u,padding:p,altBoundary:h}),b=yt(i.placement),y=Ft(i.placement),w=!y,E=Dt(b),x="x"===E?"y":"x",C=i.modifiersData.popperOffsets,S=i.rects.reference,T=i.rects.popper,A="function"==typeof _?_(Object.assign({},i.rects,{placement:i.placement})):_,O="number"==typeof A?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),k=i.modifiersData.offset?i.modifiersData.offset[i.placement]:null,j={x:0,y:0};if(C){if(a){var N,L="y"===E?ze:qe,I="y"===E?Ge:We,P="y"===E?"height":"width",D=C[E],M=D+v[L],R=D-v[I],H=f?-T[P]/2:0,$=y===Ke?S[P]:T[P],F=y===Ke?-T[P]:-S[P],V=i.elements.arrow,B=f&&V?At(V):{width:0,height:0},U=i.modifiersData["arrow#persistent"]?i.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},z=U[L],G=U[I],W=Mt(0,S[P],B[P]),q=w?S[P]/2-H-W-z-O.mainAxis:$-W-z-O.mainAxis,Y=w?-S[P]/2+H+W+G+O.mainAxis:F+W+G+O.mainAxis,Z=i.elements.arrow&&Pt(i.elements.arrow),K=Z?"y"===E?Z.clientTop||0:Z.clientLeft||0:0,J=null!=(N=null==k?void 0:k[E])?N:0,Q=D+Y-J,X=Mt(f?Et(M,D+q-J-K):M,D,f?wt(R,Q):R);C[E]=X,j[E]=X-D}if(l){var ee,te="x"===E?ze:qe,ne="x"===E?Ge:We,ie=C[x],se="y"===x?"height":"width",oe=ie+v[te],re=ie-v[ne],ae=-1!==[ze,qe].indexOf(b),ce=null!=(ee=null==k?void 0:k[x])?ee:0,le=ae?oe:ie-S[se]-T[se]-ce+O.altAxis,de=ae?ie+S[se]+T[se]-ce-O.altAxis:re,ue=f&&ae?(n=Mt(le,ie,t=de))>t?t:n:Mt(f?le:oe,ie,f?de:re);C[x]=ue,j[x]=ue-ie}i.modifiersData[o]=j}},requiresIfExists:["offset"]};function mn(e,t,n){void 0===n&&(n=!1);var i,s,o,r,a,c,l=_t(t),d=_t(t)&&(r=(o=t).getBoundingClientRect(),a=xt(r.width)/o.offsetWidth||1,c=xt(r.height)/o.offsetHeight||1,1!==a||1!==c),u=Nt(t),h=Tt(e,d,n),p={scrollLeft:0,scrollTop:0},m={x:0,y:0};return(l||!l&&!n)&&(("body"!==mt(t)||Qt(u))&&(p=(i=t)!==ft(i)&&_t(i)?{scrollLeft:(s=i).scrollLeft,scrollTop:s.scrollTop}:Kt(i)),_t(t)?((m=Tt(t,!0)).x+=t.clientLeft,m.y+=t.clientTop):u&&(m.x=Jt(u))),{x:h.left+p.scrollLeft-m.x,y:h.top+p.scrollTop-m.y,width:h.width,height:h.height}}function fn(e){var t=new Map,n=new Set,i=[];function s(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var i=t.get(e);i&&s(i)}})),i.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||s(e)})),i}var gn={placement:"bottom",modifiers:[],strategy:"absolute"};function _n(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function vn(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,i=void 0===n?[]:n,s=t.defaultOptions,o=void 0===s?gn:s;return function(e,t,n){void 0===n&&(n=o);var s,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},gn,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},c=[],l=!1,d={state:a,setOptions:function(n){var s="function"==typeof n?n(a.options):n;u(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:gt(e)?en(e):e.contextElement?en(e.contextElement):[],popper:en(t)};var r,l,h,p,m=(r=[].concat(i,a.options.modifiers),l=r.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),h=Object.keys(l).map((function(e){return l[e]})),p=fn(h),pt.reduce((function(e,t){return e.concat(p.filter((function(e){return e.phase===t})))}),[]));return a.orderedModifiers=m.filter((function(e){return e.enabled})),a.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,i=void 0===n?{}:n,s=e.effect;if("function"==typeof s){var o=s({state:a,name:t,instance:d,options:i});c.push(o||function(){})}})),d.update()},forceUpdate:function(){if(!l){var e=a.elements,t=e.reference,n=e.popper;if(_n(t,n)){a.rects={reference:mn(t,Pt(n),"fixed"===a.options.strategy),popper:At(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(e){return a.modifiersData[e.name]=Object.assign({},e.data)}));for(var i=0;i<a.orderedModifiers.length;i++)if(!0!==a.reset){var s=a.orderedModifiers[i],o=s.fn,r=s.options,c=void 0===r?{}:r,u=s.name;"function"==typeof o&&(a=o({state:a,options:c,name:u,instance:d})||a)}else a.reset=!1,i=-1}}},update:(s=function(){return new Promise((function(e){d.forceUpdate(),e(a)}))},function(){return r||(r=new Promise((function(e){Promise.resolve().then((function(){r=void 0,e(s())}))}))),r}),destroy:function(){u(),l=!0}};if(!_n(e,t))return d;function u(){c.forEach((function(e){return e()})),c=[]}return d.setOptions(n).then((function(e){!l&&n.onFirstUpdate&&n.onFirstUpdate(e)})),d}}var bn=vn(),yn=vn({defaultModifiers:[Gt,hn,Ut,bt]}),wn=vn({defaultModifiers:[Gt,hn,Ut,bt,un,an,pn,$t,dn]});const En=Object.freeze(Object.defineProperty({__proto__:null,afterMain:lt,afterRead:rt,afterWrite:ht,applyStyles:bt,arrow:$t,auto:Ye,basePlacements:Ze,beforeMain:at,beforeRead:st,beforeWrite:dt,bottom:Ge,clippingParents:Qe,computeStyles:Ut,createPopper:wn,createPopperBase:bn,createPopperLite:yn,detectOverflow:on,end:Je,eventListeners:Gt,flip:an,hide:dn,left:qe,main:ct,modifierPhases:pt,offset:un,placements:it,popper:et,popperGenerator:vn,popperOffsets:hn,preventOverflow:pn,read:ot,reference:tt,right:We,start:Ke,top:ze,variationPlacements:nt,viewport:Xe,write:ut},Symbol.toStringTag,{value:"Module"})),xn="dropdown",Cn=".bs.dropdown",Sn=".data-api",Tn="ArrowUp",An="ArrowDown",On=`hide${Cn}`,kn=`hidden${Cn}`,jn=`show${Cn}`,Nn=`shown${Cn}`,Ln=`click${Cn}${Sn}`,In=`keydown${Cn}${Sn}`,Pn=`keyup${Cn}${Sn}`,Dn="show",Mn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Rn=`${Mn}.${Dn}`,Hn=".dropdown-menu",$n=m()?"top-end":"top-start",Fn=m()?"top-start":"top-end",Vn=m()?"bottom-end":"bottom-start",Bn=m()?"bottom-start":"bottom-end",Un=m()?"left-start":"right-start",zn=m()?"right-start":"left-start",Gn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Wn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class qn extends F{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=B.next(this._element,Hn)[0]||B.prev(this._element,Hn)[0]||B.findOne(Hn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Gn}static get DefaultType(){return Wn}static get NAME(){return xn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(c(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!P.trigger(this._element,jn,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const e of[].concat(...document.body.children))P.on(e,"mouseover",d);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Dn),this._element.classList.add(Dn),P.trigger(this._element,Nn,e)}}hide(){if(c(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!P.trigger(this._element,On,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))P.off(e,"mouseover",d);this._popper&&this._popper.destroy(),this._menu.classList.remove(Dn),this._element.classList.remove(Dn),this._element.setAttribute("aria-expanded","false"),H.removeDataAttribute(this._menu,"popper"),P.trigger(this._element,kn,e),this._element.focus()}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!o(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw new TypeError(`${xn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(void 0===En)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let e=this._element;"parent"===this._config.reference?e=this._parent:o(this._config.reference)?e=r(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=wn(e,this._menu,t)}_isShown(){return this._menu.classList.contains(Dn)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return Un;if(e.classList.contains("dropstart"))return zn;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?Fn:$n:t?Bn:Vn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(H.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...g(this._config.popperConfig,[void 0,e])}}_selectMenuItem({key:e,target:t}){const n=B.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((e=>a(e)));n.length&&v(n,t,e===An,!n.includes(t)).focus()}static jQueryInterface(e){return this.each((function(){const t=qn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}static clearMenus(e){if(2===e.button||"keyup"===e.type&&"Tab"!==e.key)return;const t=B.find(Rn);for(const n of t){const t=qn.getInstance(n);if(!t||!1===t._config.autoClose)continue;const i=e.composedPath(),s=i.includes(t._menu);if(i.includes(t._element)||"inside"===t._config.autoClose&&!s||"outside"===t._config.autoClose&&s)continue;if(t._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const o={relatedTarget:t._element};"click"===e.type&&(o.clickEvent=e),t._completeHide(o)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),n="Escape"===e.key,i=[Tn,An].includes(e.key);if(!i&&!n)return;if(t&&!n)return;e.preventDefault();const s=this.matches(Mn)?this:B.prev(this,Mn)[0]||B.next(this,Mn)[0]||B.findOne(Mn,e.delegateTarget.parentNode),o=qn.getOrCreateInstance(s);if(i)return e.stopPropagation(),o.show(),void o._selectMenuItem(e);o._isShown()&&(e.stopPropagation(),o.hide(),s.focus())}}P.on(document,In,Mn,qn.dataApiKeydownHandler),P.on(document,In,Hn,qn.dataApiKeydownHandler),P.on(document,Ln,qn.clearMenus),P.on(document,Pn,qn.clearMenus),P.on(document,Ln,Mn,(function(e){e.preventDefault(),qn.getOrCreateInstance(this).toggle()})),f(qn);const Yn="backdrop",Zn="show",Kn=`mousedown.bs.${Yn}`,Jn={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Qn={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Xn extends ${constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return Jn}static get DefaultType(){return Qn}static get NAME(){return Yn}show(e){if(!this._config.isVisible)return void g(e);this._append();const t=this._getElement();this._config.isAnimated&&u(t),t.classList.add(Zn),this._emulateAnimation((()=>{g(e)}))}hide(e){this._config.isVisible?(this._getElement().classList.remove(Zn),this._emulateAnimation((()=>{this.dispose(),g(e)}))):g(e)}dispose(){this._isAppended&&(P.off(this._element,Kn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=r(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),P.on(e,Kn,(()=>{g(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(e){_(e,this._getElement(),this._config.isAnimated)}}const ei=".bs.focustrap",ti=`focusin${ei}`,ni=`keydown.tab${ei}`,ii="backward",si={autofocus:!0,trapElement:null},oi={autofocus:"boolean",trapElement:"element"};class ri extends ${constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return si}static get DefaultType(){return oi}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),P.off(document,ei),P.on(document,ti,(e=>this._handleFocusin(e))),P.on(document,ni,(e=>this._handleKeydown(e))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,P.off(document,ei))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=B.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===ii?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?ii:"forward")}}const ai=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",ci=".sticky-top",li="padding-right",di="margin-right";class ui{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,li,(t=>t+e)),this._setElementAttributes(ai,li,(t=>t+e)),this._setElementAttributes(ci,di,(t=>t-e))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,li),this._resetElementAttributes(ai,li),this._resetElementAttributes(ci,di)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const i=this.getWidth();this._applyManipulationCallback(e,(e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+i)return;this._saveInitialAttribute(e,t);const s=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${n(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&H.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,(e=>{const n=H.getDataAttribute(e,t);null!==n?(H.removeDataAttribute(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)}))}_applyManipulationCallback(e,t){if(o(e))t(e);else for(const n of B.find(e,this._element))t(n)}}const hi=".bs.modal",pi=`hide${hi}`,mi=`hidePrevented${hi}`,fi=`hidden${hi}`,gi=`show${hi}`,_i=`shown${hi}`,vi=`resize${hi}`,bi=`click.dismiss${hi}`,yi=`mousedown.dismiss${hi}`,wi=`keydown.dismiss${hi}`,Ei=`click${hi}.data-api`,xi="modal-open",Ci="show",Si="modal-static",Ti={backdrop:!0,focus:!0,keyboard:!0},Ai={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Oi extends F{constructor(e,t){super(e,t),this._dialog=B.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new ui,this._addEventListeners()}static get Default(){return Ti}static get DefaultType(){return Ai}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||P.trigger(this._element,gi,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(xi),this._adjustDialog(),this._backdrop.show((()=>this._showElement(e))))}hide(){this._isShown&&!this._isTransitioning&&(P.trigger(this._element,pi).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Ci),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){P.off(window,hi),P.off(this._dialog,hi),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Xn({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new ri({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=B.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),u(this._element),this._element.classList.add(Ci),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,P.trigger(this._element,_i,{relatedTarget:e})}),this._dialog,this._isAnimated())}_addEventListeners(){P.on(this._element,wi,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),P.on(window,vi,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),P.on(this._element,yi,(e=>{P.one(this._element,bi,(t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(xi),this._resetAdjustments(),this._scrollBar.reset(),P.trigger(this._element,fi)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(P.trigger(this._element,mi).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(Si)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(Si),this._queueCallback((()=>{this._element.classList.remove(Si),this._queueCallback((()=>{this._element.style.overflowY=t}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const e=m()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!n&&e){const e=m()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each((function(){const n=Oi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e](t)}}))}}P.on(document,Ei,'[data-bs-toggle="modal"]',(function(e){const t=B.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),P.one(t,gi,(e=>{e.defaultPrevented||P.one(t,fi,(()=>{a(this)&&this.focus()}))}));const n=B.findOne(".modal.show");n&&Oi.getInstance(n).hide(),Oi.getOrCreateInstance(t).toggle(this)})),U(Oi),f(Oi);const ki=".bs.offcanvas",ji=".data-api",Ni=`load${ki}${ji}`,Li="show",Ii="showing",Pi="hiding",Di=".offcanvas.show",Mi=`show${ki}`,Ri=`shown${ki}`,Hi=`hide${ki}`,$i=`hidePrevented${ki}`,Fi=`hidden${ki}`,Vi=`resize${ki}`,Bi=`click${ki}${ji}`,Ui=`keydown.dismiss${ki}`,zi={backdrop:!0,keyboard:!0,scroll:!1},Gi={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Wi extends F{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return zi}static get DefaultType(){return Gi}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||P.trigger(this._element,Mi,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new ui).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Ii),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(Li),this._element.classList.remove(Ii),P.trigger(this._element,Ri,{relatedTarget:e})}),this._element,!0))}hide(){this._isShown&&(P.trigger(this._element,Hi).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Pi),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(Li,Pi),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new ui).reset(),P.trigger(this._element,Fi)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=Boolean(this._config.backdrop);return new Xn({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():P.trigger(this._element,$i)}:null})}_initializeFocusTrap(){return new ri({trapElement:this._element})}_addEventListeners(){P.on(this._element,Ui,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():P.trigger(this._element,$i))}))}static jQueryInterface(e){return this.each((function(){const t=Wi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}P.on(document,Bi,'[data-bs-toggle="offcanvas"]',(function(e){const t=B.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),c(this))return;P.one(t,Fi,(()=>{a(this)&&this.focus()}));const n=B.findOne(Di);n&&n!==t&&Wi.getInstance(n).hide(),Wi.getOrCreateInstance(t).toggle(this)})),P.on(window,Ni,(()=>{for(const e of B.find(Di))Wi.getOrCreateInstance(e).show()})),P.on(window,Vi,(()=>{for(const e of B.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&Wi.getOrCreateInstance(e).hide()})),U(Wi),f(Wi);const qi={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Yi=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Zi=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Ki=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?!Yi.has(n)||Boolean(Zi.test(e.nodeValue)):t.filter((e=>e instanceof RegExp)).some((e=>e.test(n)))},Ji={allowList:qi,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Qi={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Xi={entry:"(string|element|function|null)",selector:"(string|element)"};class es extends ${constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return Ji}static get DefaultType(){return Qi}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((e=>this._resolvePossibleFunction(e))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[i,s]of Object.entries(this._config.content))this._setContent(e,s,i);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},Xi)}_setContent(e,t,n){const i=B.findOne(n,e);i&&((t=this._resolvePossibleFunction(t))?o(t)?this._putElementInTemplate(r(t),i):this._config.html?i.innerHTML=this._maybeSanitize(t):i.textContent=t:i.remove())}_maybeSanitize(e){return this._config.sanitize?function(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const i=(new window.DOMParser).parseFromString(e,"text/html"),s=[].concat(...i.body.querySelectorAll("*"));for(const o of s){const e=o.nodeName.toLowerCase();if(!Object.keys(t).includes(e)){o.remove();continue}const n=[].concat(...o.attributes),i=[].concat(t["*"]||[],t[e]||[]);for(const t of n)Ki(t,i)||o.removeAttribute(t.nodeName)}return i.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return g(e,[void 0,this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const ts=new Set(["sanitize","allowList","sanitizeFn"]),ns="fade",is="show",ss=".tooltip-inner",os=".modal",rs="hide.bs.modal",as="hover",cs="focus",ls={AUTO:"auto",TOP:"top",RIGHT:m()?"left":"right",BOTTOM:"bottom",LEFT:m()?"right":"left"},ds={allowList:qi,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},us={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class hs extends F{constructor(e,t){if(void 0===En)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return ds}static get DefaultType(){return us}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),P.off(this._element.closest(os),rs,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=P.trigger(this._element,this.constructor.eventName("show")),t=(l(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:i}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(i.append(n),P.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(is),"ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))P.on(s,"mouseover",d);this._queueCallback((()=>{P.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!P.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(is),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))P.off(e,"mouseover",d);this._activeTrigger.click=!1,this._activeTrigger[cs]=!1,this._activeTrigger[as]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),P.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(ns,is),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(e=>{do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(ns),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new es({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ss]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ns)}_isShown(){return this.tip&&this.tip.classList.contains(is)}_createPopper(e){const t=g(this._config.placement,[this,e,this._element]),n=ls[t.toUpperCase()];return wn(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return g(e,[this._element,this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...g(this._config.popperConfig,[void 0,t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)P.on(this._element,this.constructor.eventName("click"),this._config.selector,(e=>{this._initializeOnDelegatedTarget(e).toggle()}));else if("manual"!==t){const e=t===as?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=t===as?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");P.on(this._element,e,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?cs:as]=!0,t._enter()})),P.on(this._element,n,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?cs:as]=t._element.contains(e.relatedTarget),t._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},P.on(this._element.closest(os),rs,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=H.getDataAttributes(this._element);for(const n of Object.keys(t))ts.has(n)&&delete t[n];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:r(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each((function(){const t=hs.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}f(hs);const ps=".popover-header",ms=".popover-body",fs={...hs.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},gs={...hs.DefaultType,content:"(null|string|element|function)"};class _s extends hs{static get Default(){return fs}static get DefaultType(){return gs}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[ps]:this._getTitle(),[ms]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each((function(){const t=_s.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}f(_s);const vs=".bs.scrollspy",bs=`activate${vs}`,ys=`click${vs}`,ws=`load${vs}.data-api`,Es="active",xs="[href]",Cs=".nav-link",Ss=`${Cs}, .nav-item > ${Cs}, .list-group-item`,Ts={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},As={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Os extends F{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ts}static get DefaultType(){return As}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=r(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map((e=>Number.parseFloat(e)))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(P.off(this._config.target,ys),P.on(this._config.target,ys,xs,(e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const n=this._rootElement||window,i=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:i,behavior:"smooth"});n.scrollTop=i}})))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((e=>this._observerCallback(e)),e)}_observerCallback(e){const t=e=>this._targetLinks.get(`#${e.target.id}`),n=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},i=(this._rootElement||document.documentElement).scrollTop,s=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const o of e){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(o));continue}const e=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&e){if(n(o),!i)return}else s||e||n(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=B.find(xs,this._config.target);for(const t of e){if(!t.hash||c(t))continue;const e=B.findOne(decodeURI(t.hash),this._element);a(e)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,e))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(Es),this._activateParents(e),P.trigger(this._element,bs,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))B.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(Es);else for(const t of B.parents(e,".nav, .list-group"))for(const e of B.prev(t,Ss))e.classList.add(Es)}_clearActiveClass(e){e.classList.remove(Es);const t=B.find(`${xs}.${Es}`,e);for(const n of t)n.classList.remove(Es)}static jQueryInterface(e){return this.each((function(){const t=Os.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}P.on(window,ws,(()=>{for(const e of B.find('[data-bs-spy="scroll"]'))Os.getOrCreateInstance(e)})),f(Os);const ks=".bs.tab",js=`hide${ks}`,Ns=`hidden${ks}`,Ls=`show${ks}`,Is=`shown${ks}`,Ps=`click${ks}`,Ds=`keydown${ks}`,Ms=`load${ks}`,Rs="ArrowLeft",Hs="ArrowRight",$s="ArrowUp",Fs="ArrowDown",Vs="Home",Bs="End",Us="active",zs="fade",Gs="show",Ws=".dropdown-toggle",qs=`:not(${Ws})`,Ys='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Zs=`.nav-link${qs}, .list-group-item${qs}, [role="tab"]${qs}, ${Ys}`,Ks=`.${Us}[data-bs-toggle="tab"], .${Us}[data-bs-toggle="pill"], .${Us}[data-bs-toggle="list"]`;class Js extends F{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),P.on(this._element,Ds,(e=>this._keydown(e))))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),n=t?P.trigger(t,js,{relatedTarget:e}):null;P.trigger(e,Ls,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(Us),this._activate(B.getElementFromSelector(e)),this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),P.trigger(e,Is,{relatedTarget:t})):e.classList.add(Gs)}),e,e.classList.contains(zs)))}_deactivate(e,t){e&&(e.classList.remove(Us),e.blur(),this._deactivate(B.getElementFromSelector(e)),this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),P.trigger(e,Ns,{relatedTarget:t})):e.classList.remove(Gs)}),e,e.classList.contains(zs)))}_keydown(e){if(![Rs,Hs,$s,Fs,Vs,Bs].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter((e=>!c(e)));let n;if([Vs,Bs].includes(e.key))n=t[e.key===Vs?0:t.length-1];else{const i=[Hs,Fs].includes(e.key);n=v(t,e.target,i,!0)}n&&(n.focus({preventScroll:!0}),Js.getOrCreateInstance(n).show())}_getChildren(){return B.find(Zs,this._parent)}_getActiveElem(){return this._getChildren().find((e=>this._elemIsActive(e)))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const n of t)this._setInitialAttributesOnChild(n)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=B.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;const i=(e,i)=>{const s=B.findOne(e,n);s&&s.classList.toggle(i,t)};i(Ws,Us),i(".dropdown-menu",Gs),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(Us)}_getInnerElement(e){return e.matches(Zs)?e:B.findOne(Zs,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each((function(){const t=Js.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}P.on(document,Ps,Ys,(function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),c(this)||Js.getOrCreateInstance(this).show()})),P.on(window,Ms,(()=>{for(const e of B.find(Ks))Js.getOrCreateInstance(e)})),f(Js);const Qs=".bs.toast",Xs=`mouseover${Qs}`,eo=`mouseout${Qs}`,to=`focusin${Qs}`,no=`focusout${Qs}`,io=`hide${Qs}`,so=`hidden${Qs}`,oo=`show${Qs}`,ro=`shown${Qs}`,ao="hide",co="show",lo="showing",uo={animation:"boolean",autohide:"boolean",delay:"number"},ho={animation:!0,autohide:!0,delay:5e3};class po extends F{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return ho}static get DefaultType(){return uo}static get NAME(){return"toast"}show(){P.trigger(this._element,oo).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(ao),u(this._element),this._element.classList.add(co,lo),this._queueCallback((()=>{this._element.classList.remove(lo),P.trigger(this._element,ro),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(P.trigger(this._element,io).defaultPrevented||(this._element.classList.add(lo),this._queueCallback((()=>{this._element.classList.add(ao),this._element.classList.remove(lo,co),P.trigger(this._element,so)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(co),super.dispose()}isShown(){return this._element.classList.contains(co)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){P.on(this._element,Xs,(e=>this._onInteraction(e,!0))),P.on(this._element,eo,(e=>this._onInteraction(e,!1))),P.on(this._element,to,(e=>this._onInteraction(e,!0))),P.on(this._element,no,(e=>this._onInteraction(e,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each((function(){const t=po.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}return U(po),f(po),{Alert:q,Button:Z,Carousel:ke,Collapse:Ue,Dropdown:qn,Modal:Oi,Offcanvas:Wi,Popover:_s,ScrollSpy:Os,Tab:Js,Toast:po,Tooltip:hs}}());const ci=r(m());f(),window.$=window.jQuery=ci,document.addEventListener("DOMContentLoaded",(()=>{S((()=>import("./navbar-DKWY-dJv.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&t()})),S((()=>import("./sliders-FN4r_CE7.js")),__vite__mapDeps([56,11])).then((e=>{const t=e.default;"function"==typeof t&&t()})),S((()=>import("./lightbox-SG57LFHg.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&t()})),S((()=>import("./scroll-29t_LZvt.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&t()})),S((()=>import("./cal-integration-UFwr938F.js")),[]).then((e=>{const t=e.default;"function"==typeof t&&(t(),window.addEventListener("load",t),document.addEventListener("visibilitychange",(()=>{"visible"===document.visibilityState&&t()})))}))}));const li=document.getElementById("app"),di=()=>{li&&x.createRoot(li).render(w.jsx(c.StrictMode,{children:w.jsx(oi,{})}))};"complete"===document.readyState?di():window.addEventListener("load",di);export{xe as H,ft as a,ut as c,w as j,bt as l};
