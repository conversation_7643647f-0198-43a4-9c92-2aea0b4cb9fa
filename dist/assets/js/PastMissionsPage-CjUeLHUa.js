import{j as s}from"./main-CLnphFAT.js";import{b as e}from"./vendor-react-BRbkoAi_.js";import{S as i}from"./SEO-BKdVh8Ed.js";import{S as a}from"./SectionDivider-CGT6Tjmd.js";import{m as n,a as t,b as l,S as c}from"./mission_3-DUzPbnGl.js";import{B as o}from"./BookMission-CnnAaXCG.js";import{C as r}from"./CalButton-CGVztXjq.js";import{h as d}from"./spacemissionResearch-CIUGMkzF.js";import"./vendor-utils-DOb1KAbh.js";const m={lightboxOverlay:"_lightboxOverlay_akojd_2",lightboxContainer:"_lightboxContainer_akojd_27",closeButton:"_closeButton_akojd_50",lightboxContent:"_lightboxContent_akojd_75",imageSection:"_imageSection_akojd_83",missionImage:"_missionImage_akojd_89",missionBadge:"_missionBadge_akojd_96",missionNumber:"_missionNumber_akojd_109",missionIcon:"_missionIcon_akojd_117",contentSection:"_contentSection_akojd_122",headerContainer:"_headerContainer_akojd_132",missionTitle:"_missionTitle_akojd_136",missionSubtitle:"_missionSubtitle_akojd_149",missionDate:"_missionDate_akojd_158",divider:"_divider_akojd_168",detailsContainer:"_detailsContainer_akojd_175",sectionTitle:"_sectionTitle_akojd_181",overviewText:"_overviewText_akojd_190",significanceText:"_significanceText_akojd_191",detailsList:"_detailsList_akojd_200",detailItem:"_detailItem_akojd_209",detailIcon:"_detailIcon_akojd_220",buttonsContainer:"_buttonsContainer_akojd_227",consultationButton:"_consultationButton_akojd_235",backButton:"_backButton_akojd_265"},h=({isOpen:i,onClose:a,missionData:n})=>{const t=e.useRef(null);return e.useEffect((()=>{const s=s=>{t.current&&!t.current.contains(s.target)&&a()},e=s=>{"Escape"===s.key&&a()};return i&&(document.addEventListener("mousedown",s),document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("mousedown",s),document.removeEventListener("keydown",e),document.body.style.overflow="auto"}}),[i,a]),i&&n?s.jsx("div",{className:m.lightboxOverlay,onClick:a,children:s.jsxs("div",{className:m.lightboxContainer,ref:t,onClick:s=>s.stopPropagation(),children:[s.jsx("button",{className:m.closeButton,onClick:a,children:"×"}),s.jsxs("div",{className:m.lightboxContent,children:[s.jsxs("div",{className:m.imageSection,children:[s.jsx("img",{src:n.image,alt:n.title,className:m.missionImage}),s.jsxs("div",{className:m.missionBadge,children:[s.jsx("span",{className:m.missionNumber,children:String(n.id).padStart(2,"0")}),s.jsx("span",{className:m.missionIcon,children:"🚀"})]})]}),s.jsxs("div",{className:m.contentSection,children:[s.jsxs("div",{className:m.headerContainer,children:[s.jsx("h2",{className:m.missionTitle,children:n.title}),s.jsx("p",{className:m.missionSubtitle,children:n.subtitle}),s.jsx("p",{className:m.missionDate,children:n.date})]}),s.jsx("hr",{className:m.divider}),s.jsxs("div",{className:m.detailsContainer,children:[s.jsxs("div",{className:m.overviewSection,children:[s.jsx("h3",{className:m.sectionTitle,children:"Mission Overview"}),s.jsx("p",{className:m.overviewText,children:n.overview})]}),s.jsxs("div",{className:m.launchDetailsSection,children:[s.jsx("h3",{className:m.sectionTitle,children:"Launch Details"}),s.jsx("ul",{className:m.detailsList,children:n.launchDetails.map(((e,i)=>s.jsxs("li",{className:m.detailItem,children:[s.jsx("span",{className:m.detailIcon,children:"✓"}),e]},i)))})]}),s.jsxs("div",{className:m.specsSection,children:[s.jsx("h3",{className:m.sectionTitle,children:"Mission Specifications"}),s.jsx("ul",{className:m.detailsList,children:n.missionSpecs.map(((e,i)=>s.jsxs("li",{className:m.detailItem,children:[s.jsx("span",{className:m.detailIcon,children:"📊"}),e]},i)))})]}),s.jsxs("div",{className:m.resultsSection,children:[s.jsx("h3",{className:m.sectionTitle,children:"Mission Results"}),s.jsx("ul",{className:m.detailsList,children:n.results.map(((e,i)=>s.jsxs("li",{className:m.detailItem,children:[s.jsx("span",{className:m.detailIcon,children:"🎯"}),e]},i)))})]}),s.jsxs("div",{className:m.significanceSection,children:[s.jsx("h3",{className:m.sectionTitle,children:"Mission Significance"}),s.jsx("p",{className:m.significanceText,children:n.significance})]})]}),s.jsxs("div",{className:m.buttonsContainer,children:[s.jsx(r,{className:m.consultationButton,calLink:"researchsat-2023/30min",namespace:"30min",config:{layout:"month_view"},onCalOpen:a,children:"Schedule Consultation"}),s.jsx("button",{className:m.backButton,onClick:a,children:"Back to Missions"})]})]})]})]})}):null},u="_pastMissionsPage_mbsu3_3",p="_heroSection_mbsu3_10",x="_heroBackground_mbsu3_20",_="_gradientOverlay_mbsu3_31",v="_contentContainer_mbsu3_41",j="_heroContent_mbsu3_53",b="_pastMissionsLabel_mbsu3_57",g="_heroTitle_mbsu3_68",N="_bottomContainer_mbsu3_77",y="_descriptionContainer_mbsu3_86",S="_descriptionText_mbsu3_90",f="_secondSection_mbsu3_100",k="_sectionContainer_mbsu3_105",C="_sectionTitle_mbsu3_111",w="_grayText_mbsu3_121",A="_redText_mbsu3_125",M="_lightText_mbsu3_129",L="_missionTypesWrapper_mbsu3_134",D="_missionTypesHeader_mbsu3_139",T="_trustWallLabel_mbsu3_148",E="_missionSection_mbsu3_160",B="_missionContent_mbsu3_166",I="_missionImageContainer_mbsu3_174",O="_missionImage_mbsu3_174",F="_missionTextContent_mbsu3_193",R="_missionTitle_mbsu3_201",U="_missionSubtitle_mbsu3_210",P="_missionDescription_mbsu3_220",H="_missionHighlights_mbsu3_229",Y="_highlight_mbsu3_236",z="_highlightLabel_mbsu3_242",G="_highlightValue_mbsu3_250",q="_learnMoreButton_mbsu3_260",W="_footerMargin_mbsu3_296",X=()=>{const[r,m]=e.useState(!1),[X,V]=e.useState(null);e.useEffect((()=>{document.title="Past Missions | ResearchSat",window.scrollTo(0,0)}),[]);const J={1:{id:1,title:"Atmospheric Space Mission Launch",subtitle:"UDAAN Yeast Experiment",date:"February 2022 | Adelaide",image:n,overview:"Our first atmospheric mission successfully demonstrated ResearchSat's capability to deliver functional experimental systems in space environments. The UDAAN payload carried yeast experiments to an altitude of 25km, providing valuable insights into microgravity effects on biological systems.",launchDetails:["Launched in February 2022","ResearchSat payload: UDAAN [Yeast Experiment]","Launch provider: UP&UP - High Altitude Balloon","Launch site: Mount Barker, SA"],missionSpecs:["Flight Duration: Approx 60 secs of microgravity","Flight Altitude: max altitude is 25 km","Microgravity level: Free fall","Environment: -20°C & 0.103 atm"],results:["SUCCESSFUL LAUNCH","Flow Rate Experiment: Observed decreased flow rate","Yeast Experiment: yeast undergoes random budding rather than bipolar budding","Increased cell clumping observed"],significance:"This mission validated our payload design approaches and established our capability to execute space-based biological research."},2:{id:2,title:"Suborbital Space Mission Launch 01",subtitle:"ADI-Alpha Advanced Research",date:"November 2022 | Esrange Space Center, Sweden",image:t,overview:"Our breakthrough suborbital mission achieved 6 minutes of high-quality microgravity at 300km altitude. The ADI-Alpha payload demonstrated advanced experimental capabilities including double emulsion formation and significant biological discoveries.",launchDetails:["Mission S1X-3/M15","ResearchSat payload: ADI-Alpha","Launch provider: Swedish Space Corporation (SSC)","Launch site: Esrange Space Center, Sweden"],missionSpecs:["Flight Duration: 6 minutes of microgravity","Flight Altitude: 300 km","Microgravity level: 10^-6 g","Environment: -20°C & 0.103 atm"],results:["SUCCESSFUL LAUNCH","Observed double emulsion formation","Yeast grown 4x times with decreased cell size","100% cell viability maintained","Flow rate experiments showed decreased flow rate"],significance:"This mission provided crucial experience that directly informs our current development programs and demonstrated our ability to achieve complex experimental objectives in space."},3:{id:3,title:"Suborbital Space Mission Launch 02",subtitle:"ADI-Beta Electronics & Cell Bank",date:"February 2023 | Esrange Space Center, Sweden",image:l,overview:"Our most recent suborbital mission showcased advanced electronics payload capabilities and demonstrated our cell bank technology. Multiple microorganisms were successfully transported to space, validating our biological preservation systems.",launchDetails:["Mission S1X-4/M16","ResearchSat payload: ADI-electronica (Beta)","Launch provider: Swedish Space Corporation (SSC)","Launch site: Esrange Space Center, Sweden"],missionSpecs:["Flight Duration: 6 minutes of microgravity","Flight Altitude: 300 km","Microgravity level: 10^-6 g","Environment: -20°C & 0.103 atm"],results:["SUCCESSFUL LAUNCH","Custom Electronics Payload performed flawlessly","Multiple microbes (bacteria, yeast, fungi) carried to space","Cell viability maintained throughout mission","Demonstrated cellbank capability for future missions"],significance:"This mission established our electronics capabilities and validated our cell bank technology, paving the way for more complex biological research missions."}},K=s=>{V(J[s]),m(!0),document.body.style.overflow="hidden"};return s.jsxs(s.Fragment,{children:[s.jsx(i,{title:"Past Missions - Mission Gallery | ResearchSat",description:"Explore ResearchSat's successful space missions including atmospheric balloon launches and suborbital research missions. Detailed mission reports and results from our proven track record.",keywords:["past space missions","mission gallery","space research history","suborbital missions","atmospheric missions","space experiments"],structuredData:{"@context":"https://schema.org","@type":"Organization",name:"ResearchSat Past Missions",description:"Explore ResearchSat's proven track record with detailed mission histories, from atmospheric balloon launches to advanced suborbital research missions.",url:"https://researchsat.space/past-missions"}}),s.jsxs("div",{className:u,children:[s.jsxs("section",{className:p,children:[s.jsx("img",{src:d,alt:"Space missions background",className:x}),s.jsx("div",{className:_}),s.jsx("div",{className:v,children:s.jsxs("div",{className:j,children:[s.jsx("div",{className:b,children:"_Past Missions"}),s.jsx("h1",{className:g,children:"Our Proven Track Record in Space"})]})}),s.jsx("div",{className:N,children:s.jsx("div",{className:y,children:s.jsx("p",{className:S,children:"Explore our successful space missions that have validated our technology and demonstrated our capability to deliver groundbreaking research in space environments."})})})]}),s.jsx("section",{className:f,children:s.jsxs("div",{className:k,children:[s.jsx("h2",{className:C,children:"Proven Success in Space Environments"}),s.jsx("div",{className:v,children:s.jsxs("div",{className:S,children:[s.jsxs("span",{className:w,children:["At ",s.jsx("span",{className:A,children:"ResearchSat"}),", we have successfully executed multiple space missions, demonstrating our ability to deliver functional experimental systems in the demanding conditions of space flight.",s.jsx("br",{}),s.jsx("br",{})]}),s.jsx("span",{className:M,children:"Discover the details of our groundbreaking missions and their scientific achievements."})]})})]})}),s.jsx(a,{}),s.jsxs("div",{className:L,children:[s.jsxs("div",{className:D,children:[s.jsx("div",{className:T,children:"_Mission History"}),s.jsx("h2",{className:C,children:"Our Past Missions"})]}),s.jsx("div",{className:E,children:s.jsx("div",{className:k,children:s.jsxs("div",{className:B,children:[s.jsx("div",{className:I,children:s.jsx("img",{src:n,alt:"Atmospheric Mission",className:O})}),s.jsxs("div",{className:F,children:[s.jsx("h2",{className:R,children:"Atmospheric Space Mission Launch"}),s.jsx("h6",{className:U,children:"UDAAN Yeast Experiment - February 2022"}),s.jsx("p",{className:P,children:"Our inaugural atmospheric mission successfully demonstrated ResearchSat's capability to deliver functional experimental systems in space environments. The UDAAN payload carried yeast experiments to 25km altitude, providing valuable insights into microgravity effects on biological systems and validating our payload design approaches."}),s.jsxs("div",{className:H,children:[s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Duration:"}),s.jsx("span",{className:G,children:"60 seconds microgravity"})]}),s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Altitude:"}),s.jsx("span",{className:G,children:"25 km"})]}),s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Status:"}),s.jsx("span",{className:G,children:"Successful Launch"})]})]}),s.jsx("button",{className:q,onClick:()=>K(1),children:"Learn More"})]})]})})}),s.jsx("div",{className:E,children:s.jsx("div",{className:k,children:s.jsxs("div",{className:B,children:[s.jsxs("div",{className:F,children:[s.jsx("h2",{className:R,children:"Suborbital Space Mission Launch 01"}),s.jsx("h6",{className:U,children:"ADI-Alpha Advanced Research - November 2022"}),s.jsx("p",{className:P,children:"Our breakthrough suborbital mission achieved 6 minutes of high-quality microgravity at 300km altitude. The ADI-Alpha payload demonstrated advanced experimental capabilities including double emulsion formation and significant biological discoveries, providing crucial experience for our current development programs."}),s.jsxs("div",{className:H,children:[s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Duration:"}),s.jsx("span",{className:G,children:"6 minutes microgravity"})]}),s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Altitude:"}),s.jsx("span",{className:G,children:"300 km"})]}),s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Achievement:"}),s.jsx("span",{className:G,children:"4x Yeast Growth"})]})]}),s.jsx("button",{className:q,onClick:()=>K(2),children:"Learn More"})]}),s.jsx("div",{className:I,children:s.jsx("img",{src:t,alt:"Suborbital Mission 01",className:O})})]})})}),s.jsx("div",{className:E,children:s.jsx("div",{className:k,children:s.jsxs("div",{className:B,children:[s.jsx("div",{className:I,children:s.jsx("img",{src:l,alt:"Suborbital Mission 02",className:O})}),s.jsxs("div",{className:F,children:[s.jsx("h2",{className:R,children:"Suborbital Space Mission Launch 02"}),s.jsx("h6",{className:U,children:"ADI-Beta Electronics & Cell Bank - February 2023"}),s.jsx("p",{className:P,children:"Our most recent suborbital mission showcased advanced electronics payload capabilities and demonstrated our cell bank technology. Multiple microorganisms were successfully transported to space, validating our biological preservation systems and establishing our electronics capabilities for future missions."}),s.jsxs("div",{className:H,children:[s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Duration:"}),s.jsx("span",{className:G,children:"6 minutes microgravity"})]}),s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Payload:"}),s.jsx("span",{className:G,children:"Custom Electronics"})]}),s.jsxs("div",{className:Y,children:[s.jsx("span",{className:z,children:"Biology:"}),s.jsx("span",{className:G,children:"Multiple Microbes"})]})]}),s.jsx("button",{className:q,onClick:()=>K(3),children:"Learn More"})]})]})})}),s.jsx("div",{className:W})]}),s.jsx(c,{}),s.jsx(o,{})]}),s.jsx(h,{isOpen:r,onClose:()=>{m(!1),V(null),document.body.style.overflow="auto"},missionData:X})]})};export{X as default};
