import{j as e}from"./main-C7y4_twE.js";import{b as s}from"./vendor-react-BRbkoAi_.js";import{S as i}from"./SEO-CiRj42Kt.js";import{s as a}from"./spacexperiment-email-DxDf5037.js";import{h as n}from"./hero-background-DVoA4ozo.js";import"./vendor-utils-DOb1KAbh.js";const r={bookMissionPage:"_bookMissionPage_13hr9_3",notification:"_notification_13hr9_10",slideIn:"_slideIn_13hr9_1",success:"_success_13hr9_25",error:"_error_13hr9_31",info:"_info_13hr9_37",heroSection:"_heroSection_13hr9_55",heroBackground:"_heroBackground_13hr9_66",gradientOverlay:"_gradientOverlay_13hr9_77",contentContainer:"_contentContainer_13hr9_88",heroContent:"_heroContent_13hr9_96",missionLabel:"_missionLabel_13hr9_107",heroTitle:"_heroTitle_13hr9_118",bottomContainer:"_bottomContainer_13hr9_130",descriptionContainer:"_descriptionContainer_13hr9_144",descriptionText:"_descriptionText_13hr9_153",progressSection:"_progressSection_13hr9_166",container:"_container_13hr9_174",progressBar:"_progressBar_13hr9_179",progressStep:"_progressStep_13hr9_199",stepNumber:"_stepNumber_13hr9_207",active:"_active_13hr9_224",completed:"_completed_13hr9_230",stepLabel:"_stepLabel_13hr9_236",mainSection:"_mainSection_13hr9_251",formCard:"_formCard_13hr9_258",stepTitle:"_stepTitle_13hr9_268",stepContent:"_stepContent_13hr9_278",stepDescription:"_stepDescription_13hr9_282",experimentGrid:"_experimentGrid_13hr9_293",experimentCard:"_experimentCard_13hr9_299",selected:"_selected_13hr9_315",experimentIcon:"_experimentIcon_13hr9_321",experimentName:"_experimentName_13hr9_327",experimentDescription:"_experimentDescription_13hr9_336",experimentMeta:"_experimentMeta_13hr9_346",duration:"_duration_13hr9_355",complexity:"_complexity_13hr9_362",beginner:"_beginner_13hr9_372",intermediate:"_intermediate_13hr9_377",advanced:"_advanced_13hr9_382",missionGrid:"_missionGrid_13hr9_388",missionCard:"_missionCard_13hr9_394",missionName:"_missionName_13hr9_415",missionDetails:"_missionDetails_13hr9_425",missionDetail:"_missionDetail_13hr9_425",detailLabel:"_detailLabel_13hr9_443",detailValue:"_detailValue_13hr9_450",detailsForm:"_detailsForm_13hr9_458",contactForm:"_contactForm_13hr9_459",formRow:"_formRow_13hr9_464",inputGroup:"_inputGroup_13hr9_471",label:"_label_13hr9_477",input:"_input_13hr9_471",select:"_select_13hr9_315",textarea:"_textarea_13hr9_487",proposalSection:"_proposalSection_13hr9_526",proposalHeader:"_proposalHeader_13hr9_535",proposalTitle:"_proposalTitle_13hr9_544",loadingSpinner:"_loadingSpinner_13hr9_555",pulse:"_pulse_13hr9_1",regenerateButton:"_regenerateButton_13hr9_564",proposalContent:"_proposalContent_13hr9_591",proposalText:"_proposalText_13hr9_600",emailOptionsSection:"_emailOptionsSection_13hr9_612",emailOptionsTitle:"_emailOptionsTitle_13hr9_621",emailOptionsDescription:"_emailOptionsDescription_13hr9_630",emailOptionsButtons:"_emailOptionsButtons_13hr9_641",emailButton:"_emailButton_13hr9_648",copyButton:"_copyButton_13hr9_649",emailInstructions:"_emailInstructions_13hr9_674",summarySection:"_summarySection_13hr9_702",summaryTitle:"_summaryTitle_13hr9_710",summaryContent:"_summaryContent_13hr9_718",summaryItem:"_summaryItem_13hr9_724",summaryLabel:"_summaryLabel_13hr9_736",summaryValue:"_summaryValue_13hr9_743",navigationButtons:"_navigationButtons_13hr9_751",previousButton:"_previousButton_13hr9_760",nextButton:"_nextButton_13hr9_761",submitButton:"_submitButton_13hr9_762"},t=()=>{var t,o;const[l,c]=s.useState(""),[m,d]=s.useState(""),[p,h]=s.useState(""),[u,_]=s.useState(""),[x,y]=s.useState({name:"",email:"",organization:"",phone:""}),[v,g]=s.useState(1),[j,b]=s.useState({message:"",type:"",show:!1}),[N,f]=s.useState(""),[C,S]=s.useState(!1),[k,w]=s.useState(!1),[B,D]=s.useState(!1),[T,P]=s.useState(!1),[$,M]=s.useState(""),[O,I]=s.useState(null);s.useEffect((()=>{document.title="Book Your Mission | ResearchSat",window.scrollTo(0,0)}),[]);const E=[{id:"protein-crystallization",name:"Protein Crystallization",icon:"🧬",description:"Grow high-quality protein crystals in microgravity for pharmaceutical research",duration:"2-14 days",complexity:"Advanced"},{id:"cell-culture",name:"Cell Culture Studies",icon:"🔬",description:"Study cellular behavior and tissue engineering in space environment",duration:"1-7 days",complexity:"Intermediate"},{id:"material-science",name:"Material Science",icon:"⚛️",description:"Investigate material properties and crystal formation in microgravity",duration:"3-21 days",complexity:"Advanced"},{id:"plant-biology",name:"Plant Biology",icon:"🌱",description:"Research plant growth and adaptation in space conditions",duration:"7-30 days",complexity:"Beginner"},{id:"fluid-physics",name:"Fluid Physics",icon:"💧",description:"Study fluid dynamics and mixing processes in zero gravity",duration:"1-5 days",complexity:"Intermediate"},{id:"combustion",name:"Combustion Research",icon:"🔥",description:"Analyze combustion processes and flame behavior in microgravity",duration:"1-3 days",complexity:"Advanced"}],L=[{id:"suborbital",name:"Sub-orbital Flight",duration:"3-5 minutes",altitude:"100+ km",microgravity:"3-5 minutes",cost:"$$$"},{id:"orbital",name:"Low Earth Orbit",duration:"90 minutes - 30 days",altitude:"400-500 km",microgravity:"Continuous",cost:"$$$$"},{id:"iss",name:"International Space Station",duration:"1-6 months",altitude:"408 km",microgravity:"Long-term",cost:"$$$$$"}],G=(e,s="success")=>{b({message:e,type:s,show:!0}),setTimeout((()=>{b({message:"",type:"",show:!1})}),5e3)},R=async()=>{S(!0),w(!0),f("Generating your mission proposal..."),setTimeout((()=>{var e,s;try{const i=E.find((e=>e.id===l)),a=L.find((e=>e.id===m)),n=`# Book Mission Project Proposal\n\n## Executive Summary\nThis proposal outlines a ${(null==i?void 0:i.name)||"space research"} experiment using the ${(null==a?void 0:a.name)||"selected mission"} platform. The research will leverage microgravity conditions to advance scientific understanding.\n\n## Research Objectives\n- Conduct ${(null==i?void 0:i.name)||"space research"} in microgravity environment\n- Collect valuable scientific data for analysis\n- Compare results with Earth-based control experiments\n- Contribute to space research knowledge base\n\n## Mission Details\n- Platform: ${(null==a?void 0:a.name)||"Selected mission platform"}\n- Duration: ${p||(null==a?void 0:a.duration)||"To be determined"}\n- Environment: ${(null==a?void 0:a.microgravity)||"Microgravity conditions"}\n\n## Research Goals\n${u||"Research goals to be defined in collaboration with the research team."}\n\n## Technical Requirements\n- Specialized equipment for ${(null==(e=null==i?void 0:i.name)?void 0:e.toLowerCase())||"space research"}\n- Data recording and transmission systems\n- Safety protocols for mission operations\n- Ground support team coordination\n\n## Timeline\n- Phase 1: Preparation and equipment setup (4-6 weeks)\n- Phase 2: Mission execution (${(null==a?void 0:a.duration)||"Mission duration"})\n- Phase 3: Data analysis and reporting (8-12 weeks)\n- Phase 4: Publication and dissemination (4-6 weeks)\n\n## Expected Outcomes\n- Enhanced understanding of ${(null==(s=null==i?void 0:i.description)?void 0:s.toLowerCase())||"space phenomena"}\n- Potential breakthroughs in space-based research methodologies\n- Data that could lead to practical applications in various industries\n- Contribution to the growing body of microgravity research\n\n## Next Steps\nUpon approval, our team will work with you to refine the experimental parameters and prepare for mission execution.\n\nThis proposal will be further developed based on your specific requirements and mission constraints.`;f(n),G("Mission proposal generated successfully!","success")}catch(i){f("Error generating proposal. Please try again."),G("Error generating proposal. Please try again.","error")}finally{S(!1)}}),2e3)};return e.jsxs(e.Fragment,{children:[e.jsx(i,{title:"Book Your Space Research Mission",description:"Design and book your custom space research mission. From protein crystallization to material science - launch your research to new heights with ResearchSat.",keywords:["book space mission","custom space research","microgravity experiments","satellite mission booking","space research planning","orbital experiments","space biology mission","research mission design"],canonical:"https://researchsat.space/book-mission",structuredData:{"@context":"https://schema.org","@type":"Service",name:"Custom Space Research Mission Booking",description:"Design and book custom space research missions for microgravity experiments and space biology research.",provider:{"@type":"Organization",name:"ResearchSat",url:"https://researchsat.space"},serviceType:"Space Mission Planning",areaServed:"Worldwide",offers:{"@type":"Offer",description:"Custom space research missions from atmospheric to ISS platforms"}},breadcrumbs:[{name:"Home",url:"https://researchsat.space"},{name:"Book Mission",url:"https://researchsat.space/book-mission"}],ogType:"website"}),e.jsxs("div",{className:r.bookMissionPage,children:[j.show&&e.jsx("div",{className:`${r.notification} ${r[j.type]}`,children:j.message}),e.jsxs("section",{className:r.heroSection,children:[e.jsx("img",{src:n,alt:"Space background",className:r.heroBackground}),e.jsx("div",{className:r.gradientOverlay}),e.jsx("div",{className:r.contentContainer,children:e.jsxs("div",{className:r.heroContent,children:[e.jsx("div",{className:r.missionLabel,children:"_Book Mission"}),e.jsx("h1",{className:r.heroTitle,children:"Design and launch your custom space research mission with precision and expertise."})]})}),e.jsx("div",{className:r.bottomContainer,children:e.jsx("div",{className:r.descriptionContainer,children:e.jsx("div",{style:{textAlign:"right"},children:e.jsx("span",{style:{background:"linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%)",WebkitBackgroundClip:"text",backgroundClip:"text",WebkitTextFillColor:"transparent",fontSize:"16px",fontWeight:500,lineHeight:"120%",letterSpacing:"0.25px",fontFamily:"Poppins, sans-serif"},children:"...start your mission"})})})})]}),e.jsxs("section",{className:r.mainSection,children:[e.jsx("div",{className:r.progressSection,children:e.jsx("div",{className:r.container,children:e.jsx("div",{className:r.progressBar,children:[1,2,3,4].map((s=>e.jsxs("div",{className:`${r.progressStep} ${v>=s?r.active:""} ${v>s?r.completed:""}`,children:[e.jsx("div",{className:r.stepNumber,children:s}),e.jsxs("div",{className:r.stepLabel,children:[1===s&&"Experiment",2===s&&"Mission",3===s&&"Details",4===s&&"Contact"]})]},s)))})})}),e.jsx("div",{className:r.container,children:e.jsxs("div",{className:r.formCard,children:[e.jsx("h2",{className:r.stepTitle,children:(()=>{switch(v){case 1:return"Choose Your Experiment";case 2:return"Select Mission Type";case 3:return"Research Details";case 4:return"Contact Information";default:return"Book Mission"}})()}),1===v&&e.jsxs("div",{className:r.stepContent,children:[e.jsx("p",{className:r.stepDescription,children:"Select the type of experiment you want to conduct in space. Each experiment type has different requirements and capabilities."}),e.jsx("div",{className:r.experimentGrid,children:E.map((s=>e.jsxs("div",{className:`${r.experimentCard} ${l===s.id?r.selected:""}`,onClick:()=>c(s.id),children:[e.jsx("div",{className:r.experimentIcon,children:s.icon}),e.jsx("h3",{className:r.experimentName,children:s.name}),e.jsx("p",{className:r.experimentDescription,children:s.description}),e.jsxs("div",{className:r.experimentMeta,children:[e.jsxs("span",{className:r.duration,children:["Duration: ",s.duration]}),e.jsx("span",{className:`${r.complexity} ${r[s.complexity.toLowerCase()]}`,children:s.complexity})]})]},s.id)))})]}),2===v&&e.jsxs("div",{className:r.stepContent,children:[e.jsx("p",{className:r.stepDescription,children:"Choose the mission platform that best suits your experiment requirements and budget."}),e.jsx("div",{className:r.missionGrid,children:L.map((s=>e.jsxs("div",{className:`${r.missionCard} ${m===s.id?r.selected:""}`,onClick:()=>d(s.id),children:[e.jsx("h3",{className:r.missionName,children:s.name}),e.jsxs("div",{className:r.missionDetails,children:[e.jsxs("div",{className:r.missionDetail,children:[e.jsx("span",{className:r.detailLabel,children:"Duration:"}),e.jsx("span",{className:r.detailValue,children:s.duration})]}),e.jsxs("div",{className:r.missionDetail,children:[e.jsx("span",{className:r.detailLabel,children:"Altitude:"}),e.jsx("span",{className:r.detailValue,children:s.altitude})]}),e.jsxs("div",{className:r.missionDetail,children:[e.jsx("span",{className:r.detailLabel,children:"Microgravity:"}),e.jsx("span",{className:r.detailValue,children:s.microgravity})]}),e.jsxs("div",{className:r.missionDetail,children:[e.jsx("span",{className:r.detailLabel,children:"Cost:"}),e.jsx("span",{className:r.detailValue,children:s.cost})]})]})]},s.id)))})]}),3===v&&e.jsxs("div",{className:r.stepContent,children:[e.jsx("p",{className:r.stepDescription,children:"Provide details about your research objectives and experiment duration preferences."}),e.jsxs("div",{className:r.detailsForm,children:[e.jsxs("div",{className:r.inputGroup,children:[e.jsx("label",{className:r.label,children:"Experiment Duration"}),e.jsxs("select",{value:p,onChange:e=>h(e.target.value),className:r.select,children:[e.jsx("option",{value:"",children:"Select duration..."}),e.jsx("option",{value:"1-3-hours",children:"1-3 hours"}),e.jsx("option",{value:"1-day",children:"1 day"}),e.jsx("option",{value:"3-days",children:"3 days"}),e.jsx("option",{value:"1-week",children:"1 week"}),e.jsx("option",{value:"2-weeks",children:"2 weeks"}),e.jsx("option",{value:"1-month",children:"1 month"}),e.jsx("option",{value:"custom",children:"Custom duration"})]})]}),e.jsxs("div",{className:r.inputGroup,children:[e.jsx("label",{className:r.label,children:"Research Goals & Objectives"}),e.jsx("textarea",{value:u,onChange:e=>_(e.target.value),className:r.textarea,rows:"6",placeholder:"Describe your research objectives, expected outcomes, and any specific requirements for your space experiment..."})]})]})]}),4===v&&e.jsxs("div",{className:r.stepContent,children:[e.jsx("p",{className:r.stepDescription,children:"Review your AI-generated project proposal below, then provide your contact information to proceed."}),k&&e.jsxs("div",{className:r.proposalSection,children:[e.jsxs("div",{className:r.proposalHeader,children:[e.jsx("h3",{className:r.proposalTitle,children:C?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:r.loadingSpinner,children:"⚡"}),"Generating Your Mission Proposal..."]}):"🚀 Your AI-Generated Mission Proposal"}),!C&&e.jsx("button",{onClick:R,className:r.regenerateButton,disabled:C,children:"🔄 Regenerate"})]}),e.jsx("div",{className:r.proposalContent,children:e.jsx("pre",{className:r.proposalText,children:N})})]}),e.jsxs("div",{className:r.contactForm,children:[e.jsxs("div",{className:r.formRow,children:[e.jsxs("div",{className:r.inputGroup,children:[e.jsx("label",{className:r.label,children:"Full Name *"}),e.jsx("input",{type:"text",value:x.name,onChange:e=>y({...x,name:e.target.value}),className:r.input,placeholder:"Your full name"})]}),e.jsxs("div",{className:r.inputGroup,children:[e.jsx("label",{className:r.label,children:"Email Address *"}),e.jsx("input",{type:"email",value:x.email,onChange:e=>y({...x,email:e.target.value}),className:r.input,placeholder:"<EMAIL>"})]})]}),e.jsxs("div",{className:r.formRow,children:[e.jsxs("div",{className:r.inputGroup,children:[e.jsx("label",{className:r.label,children:"Organization"}),e.jsx("input",{type:"text",value:x.organization,onChange:e=>y({...x,organization:e.target.value}),className:r.input,placeholder:"University, Company, or Institution"})]}),e.jsxs("div",{className:r.inputGroup,children:[e.jsx("label",{className:r.label,children:"Phone Number"}),e.jsx("input",{type:"tel",value:x.phone,onChange:e=>y({...x,phone:e.target.value}),className:r.input,placeholder:"+****************"})]})]}),e.jsxs("div",{className:r.summarySection,children:[e.jsx("h3",{className:r.summaryTitle,children:"Proposal Summary"}),e.jsxs("div",{className:r.summaryContent,children:[e.jsxs("div",{className:r.summaryItem,children:[e.jsx("span",{className:r.summaryLabel,children:"Experiment:"}),e.jsx("span",{className:r.summaryValue,children:(null==(t=E.find((e=>e.id===l)))?void 0:t.name)||"Not selected"})]}),e.jsxs("div",{className:r.summaryItem,children:[e.jsx("span",{className:r.summaryLabel,children:"Mission:"}),e.jsx("span",{className:r.summaryValue,children:(null==(o=L.find((e=>e.id===m)))?void 0:o.name)||"Not selected"})]}),e.jsxs("div",{className:r.summaryItem,children:[e.jsx("span",{className:r.summaryLabel,children:"Duration:"}),e.jsx("span",{className:r.summaryValue,children:p||"Not specified"})]})]})]}),T&&e.jsxs("div",{className:r.emailOptionsSection,children:[e.jsx("h3",{className:r.emailOptionsTitle,children:"📧 Send Your Proposal"}),e.jsx("p",{className:r.emailOptionsDescription,children:"Choose how you'd like to send your proposal:"}),e.jsxs("div",{className:r.emailOptionsButtons,children:[e.jsx("a",{href:$,className:r.emailButton,target:"_blank",rel:"noopener noreferrer",children:"📧 Open Email Client"}),e.jsx("button",{onClick:()=>{O&&a.copyToClipboard(O)?G("Proposal copied to clipboard!","success"):G("Unable to copy to clipboard","error")},className:r.copyButton,children:"📋 Copy to Clipboard"})]}),e.jsxs("div",{className:r.emailInstructions,children:[e.jsx("p",{children:e.jsx("strong",{children:"Email Instructions:"})}),e.jsxs("ul",{children:[e.jsx("li",{children:'Click "Open Email Client" to compose an email with your proposal'}),e.jsx("li",{children:"Or copy the proposal and paste it into your preferred email application"}),e.jsxs("li",{children:["Send to: ",e.jsx("strong",{children:"<EMAIL>"})]})]})]})]})]})]}),e.jsxs("div",{className:r.navigationButtons,children:[v>1&&e.jsx("button",{onClick:()=>{v>1&&g(v-1)},className:r.previousButton,children:"← Previous"}),v<4?e.jsx("button",{onClick:async()=>{1!==v||l?2!==v||m?(3===v&&await R(),v<4&&g(v+1)):G("Please select a mission type.","error"):G("Please select an experiment type.","error")},className:r.nextButton,children:"Next →"}):e.jsx("button",{onClick:async()=>{if(x.name.trim())if(x.email.trim()&&/^\S+@\S+\.\S+$/.test(x.email)){D(!0);try{const s=E.find((e=>e.id===l)),i={experiment:s,mission:L.find((e=>e.id===m)),duration:p,researchGoals:u,contactInfo:x,generatedProposal:N,timestamp:(new Date).toISOString()};try{if(!(await a.sendEmails(i)).success)throw new Error("Email sending failed");G(`Thank you ${x.name}! Your mission proposal has been submitted successfully. Confirmation emails have been sent to both you and our team. We'll contact you within 24 hours.`,"success")}catch(e){const s=a.generateMailtoLink(i);G(`Thank you ${x.name}! Your proposal has been prepared. Please click the button below to send it via your email client, or copy the proposal details.`,"info"),P(!0),M(s),I(i)}}catch(s){G("There was an error submitting your proposal. Please try again.","error")}finally{D(!1)}}else G("Please enter a valid email address.","error");else G("Please enter your full name.","error")},className:r.submitButton,disabled:B,children:B?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:r.loadingSpinner,children:"⚡"}),"Submitting..."]}):"Submit Proposal"})]})]})})]})]})]})};export{t as default};
