import{j as e}from"./main-C7y4_twE.js";import{b as s,L as a}from"./vendor-react-BRbkoAi_.js";import"./vendor-utils-DOb1KAbh.js";const i=()=>(s.useEffect((()=>{document.title="Terms & Conditions | ResearchSat",window.scrollTo(0,0)}),[]),e.jsxs("div",{className:"terms-conditions-page",children:[e.jsx("header",{id:"header",className:"ex-header",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-12",children:e.jsx("h1",{children:"Terms & Conditions"})})})})}),e.jsx("div",{className:"container",children:e.jsx("hr",{className:"mt-5 mb-5",style:{borderColor:"#ef3b47"}})}),e.jsx("div",{className:"ex-basic-2",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-lg-10 offset-lg-1",children:[e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"Limitations Of Liability"}),e.jsx("p",{children:"ResearchSat also automatically collects and receives certain information from your computer or mobile device, including the activities you perform on our Website, the Platforms, and the Applications, the type of hardware and software you are using (for example, your operating system or browser), and information obtained from cookies. For example, each time you visit the Website or otherwise use the Services, we automatically collect your IP address, browser and device type, access times, the web page from which you came, the regions from which you navigate the web page, and the web page(s) you access (as applicable)."}),e.jsxs("p",{children:["When you first register for a ResearchSat account, and when you use the Services, we collect some ",e.jsx(a,{className:"turquoise",to:"#your-link",children:"Personal Information"})," about you such as:"]}),e.jsxs("ul",{className:"list-unstyled li-space-lg indent",children:[e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"The geographic area where you use your computer and mobile devices"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Your full name, username, and email address and other contact details"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"A unique ResearchSat user ID (an alphanumeric string) which is assigned to you upon registration"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Other optional information as part of your account profile"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Your IP Address and, when applicable, timestamp related to your consent and confirmation of consent"})]}),e.jsxs("li",{className:"media",children:[e.jsx("i",{className:"fas fa-square"}),e.jsx("div",{className:"media-body",children:"Other information submitted by you or your organizational representatives via various methods"})]})]})]}),e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"Terms And Conditions"}),e.jsx("p",{children:"Under no circumstances shall ResearchSat be liable for any direct, indirect, special, incidental or consequential damages, including, but not limited to, loss of data or profit, arising out of the use, or the inability to use, the materials on this site, even if ResearchSat or an authorized representative has been advised of the possibility of such damages. If your use of materials from this site results in the need for servicing, repair or correction of equipment or data, you assume any costs thereof."})]}),e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"Copyright & Intellectual Property"}),e.jsxs("p",{children:["All our templates inherit the GNU general public license from HTML. All .PSD & CSS files are packaged separately and are not licensed under the GPL 2.0. Instead, these files inherit ResearchSat License. These files are given to all Clients on a personal use basis. You may not offer them, ",e.jsx(a,{className:"turquoise",to:"#your-link",children:"modified or unmodified"}),", for redistribution or resale of any kind. You can't use one of our themes on a HTML domain. More on HTML Vs CSS, you can read here. You can use our templates do develop sites for your clients."]}),e.jsx("p",{children:"Services help our customers promote their products and services, marketing and advertising; engaging audiences; scheduling and publishing messages; and analyze the results."})]}),e.jsxs("div",{className:"text-container",children:[e.jsx("h3",{children:"Designer Membership And How It Applies"}),e.jsx("p",{children:"By using any of the Services, or submitting or collecting any Personal Information via the Services, you consent to the collection, transfer, storage disclosure, and use of your Personal Information in the manner set out in this Privacy Policy. If you do not consent to the use of your Personal Information in these ways, please stop using the Services."})]}),e.jsxs("div",{className:"text-container last",children:[e.jsx("h3",{children:"Cookies and Tracking Technologies"}),e.jsxs("p",{children:["ResearchSat uses tracking technology on our website, in the Applications, and in the Platforms, including mobile application identifiers and a unique ResearchSat user ID to help us recognize you across different Services, to monitor usage and web traffic routing for the Services, and to customize and improve the Services. By visiting ResearchSat or using the Services you agree to the use of cookies in your browser and HTML-based emails. Cookies are small text files placed on your device when you visit a ",e.jsx(a,{className:"turquoise",to:"#your-link",children:"web site"})," in order to track use of the site and to improve your user experience."]}),e.jsx(a,{className:"btn-outline-reg",to:"/",children:"BACK"})]})]})})})}),e.jsx("div",{className:"ex-basic-1",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-12",children:e.jsxs("div",{className:"breadcrumbs",children:[e.jsx(a,{to:"/",children:"Home"}),e.jsx("i",{className:"fa fa-angle-double-right"}),e.jsx("span",{children:"Terms & Conditions"})]})})})})})]}));export{i as default};
