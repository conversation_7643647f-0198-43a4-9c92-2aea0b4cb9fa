import{j as e}from"./main-CW_kQa0s.js";import{b as s}from"./vendor-react-BRbkoAi_.js";import{C as a}from"./CalButton-BACZrqxW.js";const n="_bookMissionSection_5p9sn_2",c="_container_5p9sn_9",t="_sectionTitle_5p9sn_18",l="_formContainer_5p9sn_31",r="_formTitle_5p9sn_40",i="_formDescription_5p9sn_50",o="_form_5p9sn_31",m="_formGroup_5p9sn_64",_="_label_5p9sn_68",d="_input_5p9sn_80",h="_textarea_5p9sn_80",p="_submitButton_5p9sn_110",u="_successMessage_5p9sn_144",j="_errorMessage_5p9sn_150",x="_imageContainer_5p9sn_157",N="_rocketImage_5p9sn_162",g="_contactInfo_5p9sn_170",f="_contactTitle_5p9sn_174",b="_emailLink_5p9sn_185",v="_phoneNumber_5p9sn_203",y="_meetingInfo_5p9sn_213",k="_meetingText_5p9sn_218",S="_scheduleButton_5p9sn_229",w=()=>{const[w,C]=s.useState({name:"",contact:"",message:""}),[B,M]=s.useState(!1),[T,q]=s.useState(null),F=e=>{const{name:s,value:a}=e.target;C((e=>({...e,[s]:a})))};return e.jsx("section",{id:"bookmission",className:n,children:e.jsxs("div",{className:c,children:[e.jsx("h2",{className:t,children:"Book a Mission"}),e.jsxs("div",{className:l,children:[e.jsx("h3",{className:r,children:"Not quite ready to pencil in a meeting?"}),e.jsx("p",{className:i,children:"No worries—we've got your back! Simply fill out our form, and we'll take it from there."}),e.jsxs("form",{onSubmit:async e=>{e.preventDefault(),M(!0);try{({success:!0}).success&&(q("success"),C({name:"",contact:"",message:""}))}catch(s){q("error")}finally{M(!1),setTimeout((()=>{q(null)}),3e3)}},className:o,children:[e.jsxs("div",{className:m,children:[e.jsx("label",{htmlFor:"name",className:_,children:"Name"}),e.jsx("input",{type:"text",id:"name",name:"name",value:w.name,onChange:F,placeholder:"Name",className:d,required:!0})]}),e.jsxs("div",{className:m,children:[e.jsx("label",{htmlFor:"contact",className:_,children:"Contact"}),e.jsx("input",{type:"text",id:"contact",name:"contact",value:w.contact,onChange:F,placeholder:"00-0000-0000",className:d,required:!0})]}),e.jsxs("div",{className:m,children:[e.jsx("label",{htmlFor:"message",className:_,children:"Message"}),e.jsx("textarea",{id:"message",name:"message",value:w.message,onChange:F,placeholder:"Enter your message here",className:h,required:!0})]}),e.jsx("button",{type:"submit",className:p,disabled:B,children:B?"Submitting...":"Submit"}),"success"===T&&e.jsx("p",{className:u,children:"Your message has been sent successfully!"}),"error"===T&&e.jsx("p",{className:j,children:"There was an error sending your message. Please try again."})]})]}),e.jsxs("div",{className:x,children:[e.jsx("img",{src:"/assets/jpg/payload_1-BGYhpv9u.jpg",alt:"Rocket Launch",className:N}),e.jsxs("div",{className:g,children:[e.jsx("p",{className:f,children:"Reach us @"}),e.jsx("a",{href:"mailto:<EMAIL>",className:b,children:"<EMAIL>"}),e.jsx("p",{className:v,children:"+61 4525 94883"})]}),e.jsxs("div",{className:y,children:[e.jsx("p",{className:k,children:"Nothing beats a good old-fashioned face-to-face chat. Schedule a call now—we know you've got a packed schedule!"}),e.jsx(a,{className:S,calLink:"researchsat-2023/30min",namespace:"30min",config:{layout:"month_view"},children:"Schedule Meet"})]})]})]})})};export{w as B};
