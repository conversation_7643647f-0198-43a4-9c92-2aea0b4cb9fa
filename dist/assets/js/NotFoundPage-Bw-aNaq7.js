import{j as e}from"./main-CLnphFAT.js";import{b as o,L as t}from"./vendor-react-BRbkoAi_.js";import"./vendor-utils-DOb1KAbh.js";const s=()=>(o.useEffect((()=>{document.title="Page Not Found | ResearchSat",window.scrollTo(0,0)}),[]),e.jsx("div",{className:"page-content",children:e.jsxs("div",{className:"container",style:{paddingTop:"8rem",paddingBottom:"4rem",textAlign:"center"},children:[e.jsx("h1",{children:"404 - Page Not Found"}),e.jsx("p",{children:"The page you are looking for does not exist."}),e.jsx(t,{to:"/",className:"btn-solid-reg",children:"Return to Home"})]})}));export{s as default};
