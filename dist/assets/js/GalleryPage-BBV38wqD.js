import{j as e}from"./main-CW_kQa0s.js";import{b as s}from"./vendor-react-BRbkoAi_.js";import{S as a}from"./SEO-CxymcHHH.js";import"./vendor-utils-DOb1KAbh.js";const i={galleryPage:"_galleryPage_lw04i_2",heroSection:"_heroSection_lw04i_7",heroBackground:"_heroBackground_lw04i_18",gradientOverlay:"_gradientOverlay_lw04i_29",contentContainer:"_contentContainer_lw04i_40",heroContent:"_heroContent_lw04i_48",galleryLabel:"_galleryLabel_lw04i_59",heroTitle:"_heroTitle_lw04i_70",bottomContainer:"_bottomContainer_lw04i_82",descriptionContainer:"_descriptionContainer_lw04i_96",descriptionText:"_descriptionText_lw04i_105",gallerySection:"_gallerySection_lw04i_118",container:"_container_lw04i_127",sectionTitle:"_sectionTitle_lw04i_133",bentoGrid:"_bentoGrid_lw04i_155",bentoItem:"_bentoItem_lw04i_163",hero:"_hero_lw04i_7",large:"_large_lw04i_184",medium:"_medium_lw04i_189",small:"_small_lw04i_194",bentoImage:"_bentoImage_lw04i_199",imageOverlay:"_imageOverlay_lw04i_210",imageCategory:"_imageCategory_lw04i_233",ctaSection:"_ctaSection_lw04i_251",ctaContent:"_ctaContent_lw04i_257",ctaTitle:"_ctaTitle_lw04i_262",ctaDescription:"_ctaDescription_lw04i_270",ctaButtons:"_ctaButtons_lw04i_277",primaryButton:"_primaryButton_lw04i_284",secondaryButton:"_secondaryButton_lw04i_285",footerMargin:"_footerMargin_lw04i_319",slideshowOverlay:"_slideshowOverlay_lw04i_324",slideshowContainer:"_slideshowContainer_lw04i_338",closeButton:"_closeButton_lw04i_349",navButton:"_navButton_lw04i_373",slideshowImageContainer:"_slideshowImageContainer_lw04i_397",slideshowImage:"_slideshowImage_lw04i_397",slideshowInfo:"_slideshowInfo_lw04i_416",slideshowTitle:"_slideshowTitle_lw04i_422",slideshowCounter:"_slideshowCounter_lw04i_429",thumbnailContainer:"_thumbnailContainer_lw04i_435",thumbnail:"_thumbnail_lw04i_435",activeThumbnail:"_activeThumbnail_lw04i_480"},t="/assets/jpeg/galleryhr%20Large-CQ8L1nAm.jpeg",l="/assets/jpeg/galimg001%20Large-uSTJCIdA.jpeg",o="/assets/jpeg/galimg002%20Large-L5ZrRvwm.jpeg",r="/assets/jpeg/galimg003%20Large-D-3Jw-xV.jpeg",n="/assets/jpeg/galimg004%20Large-DDWdKYUr.jpeg",c="/assets/jpeg/galimg005%20Large-CgPNc28A.jpeg",m="/assets/jpeg/galimg006%20Large-BqTfHOuS.jpeg",g="/assets/jpeg/galimg007%20Large-CrMg7PQ2.jpeg",d="/assets/jpeg/galimg008%20Large-B-KTehib.jpeg",p="/assets/jpeg/galimg009%20Large-C1jV_RWs.jpeg",h="/assets/jpeg/Figure001%20Large-CllcosMe.jpeg",_="/assets/jpeg/Figure020%20Large-DwvYeQDW.jpeg",u="/assets/jpeg/Titanium_Box_croped%20Large-D2dXqrVn.jpeg",j="/assets/jpeg/abhrMob%20Large-3BnHv6ry.jpeg",y="/assets/jpeg/serv1%20Large-CulX5ToR.jpeg",x="/assets/jpeg/IMG_0225%20Large-CzKyIuDF.jpeg",v="/assets/jpeg/IMG_0762%20Large-Cf12ZVLj.jpeg",w="/assets/jpeg/IMG_0787%20Large-Ok9W3FYx.jpeg",b="/assets/jpeg/IMG_1517%20Large-DWvcqRIo.jpeg",C="/assets/jpeg/IMG_1536%20Large-DVl2PJCd.jpeg",N="/assets/jpeg/IMG_1556%20Large-DECorS1i.jpeg",L="/assets/jpeg/IMG_2571%20Large-CMZXAj3X.jpeg",k="/assets/jpeg/IMG_3282%20Large-CJzri68h.jpeg",T="/assets/jpeg/IMG_3356%20(1)%20Large-C_lE1dJZ.jpeg",S="/assets/jpeg/IMG_4752%20Large-CeoNdBvr.jpeg",f="/assets/jpeg/IMG_4950%20Large-B9vtOGko.jpeg",I="/assets/jpeg/IMG_6948%20(1)%20Large-PdsAP1_k.jpeg",B="/assets/jpeg/IMG_6949%20Large-DkZ_tOoF.jpeg",R=()=>{var R,M,P;const[D,G]=s.useState(!1),[A,O]=s.useState(0),[E,W]=s.useState([]);s.useEffect((()=>{window.scrollTo(0,0)}),[]);const $=[{src:h,alt:"Scientific Figure and Data Analysis",position:"hero"},{src:_,alt:"Mission Data and Results",position:"large"},{src:l,alt:"Research Laboratory Setup",position:"medium"},{src:o,alt:"Laboratory Equipment",position:"medium"},{src:r,alt:"Research Setup",position:"small"},{src:n,alt:"Technical Work",position:"small"},{src:c,alt:"Research Process",position:"medium"},{src:m,alt:"Space Research Equipment",position:"small"},{src:g,alt:"Laboratory Research Process",position:"large"},{src:d,alt:"Team Collaboration Session",position:"small"}],F=[{src:x,alt:"Team Member Portrait",position:"hero"},{src:v,alt:"Team Working",position:"large"},{src:b,alt:"Team Discussion",position:"medium"},{src:C,alt:"Research Activity",position:"medium"},{src:N,alt:"Payload Testing",position:"small"},{src:L,alt:"Payload Assembly",position:"small"},{src:k,alt:"Laboratory Work",position:"large"},{src:T,alt:"Technical Setup",position:"small"},{src:S,alt:"Research Documentation",position:"medium"},{src:f,alt:"Laboratory Process",position:"small"},{src:I,alt:"Research Equipment",position:"small"},{src:B,alt:"Technical Work",position:"medium"}],q=[{src:u,alt:"Titanium Payload Container",position:"hero"},{src:j,alt:"Mobile Research Platform",position:"large"},{src:y,alt:"Space Services and Operations",position:"large"},{src:w,alt:"Payload Development",position:"medium"},{src:p,alt:"Advanced Research Facility",position:"medium"},{src:h,alt:"Scientific Figure and Data Analysis",position:"small"},{src:_,alt:"Mission Data and Results",position:"small"},{src:l,alt:"Research Laboratory Setup",position:"medium"},{src:o,alt:"Laboratory Equipment",position:"small"},{src:r,alt:"Research Setup",position:"small"}],z=[{src:n,alt:"Technical Work",position:"hero"},{src:c,alt:"Research Process",position:"large"},{src:m,alt:"Space Research Equipment",position:"large"},{src:g,alt:"Laboratory Research Process",position:"medium"},{src:d,alt:"Team Collaboration Session",position:"medium"},{src:p,alt:"Advanced Research Facility",position:"medium"},{src:x,alt:"Team Member Portrait",position:"small"},{src:v,alt:"Team Working",position:"small"},{src:w,alt:"Payload Development",position:"medium"},{src:b,alt:"Team Discussion",position:"small"},{src:C,alt:"Research Activity",position:"small"},{src:N,alt:"Payload Testing",position:"small"},{src:L,alt:"Payload Assembly",position:"medium"},{src:k,alt:"Laboratory Work",position:"small"},{src:T,alt:"Technical Setup",position:"medium"},{src:S,alt:"Research Documentation",position:"small"},{src:f,alt:"Laboratory Process",position:"medium"},{src:I,alt:"Research Equipment",position:"small"},{src:B,alt:"Technical Work",position:"small"},{src:u,alt:"Titanium Payload Container",position:"large"},{src:j,alt:"Mobile Research Platform",position:"medium"},{src:y,alt:"Space Services and Operations",position:"small"}];s.useEffect((()=>{const e=[...$,...F,...q,...z];W(e)}),[]);const J=s.useCallback(((e,s,a=0)=>{O(a+e),G(!0),document.body.style.overflow="hidden"}),[]),V=s.useCallback((()=>{G(!1),document.body.style.overflow="unset"}),[]),Z=s.useCallback((()=>{O((e=>(e+1)%E.length))}),[E.length]),H=s.useCallback((()=>{O((e=>(e-1+E.length)%E.length))}),[E.length]);s.useEffect((()=>{const e=e=>{if(D)switch(e.key){case"ArrowRight":Z();break;case"ArrowLeft":H();break;case"Escape":V()}};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[D,Z,H,V]);const X=e=>{switch(e){case"mission":default:return 0;case"team":return $.length;case"payload":return $.length+F.length;case"activity":return $.length+F.length+q.length}};return e.jsxs(e.Fragment,{children:[e.jsx(a,{title:"Gallery - Space Biology Research Images",description:"Explore ResearchSat's comprehensive gallery showcasing space missions, research team, cutting-edge payloads, and groundbreaking microgravity research activities.",keywords:["space research gallery","microgravity experiments photos","satellite missions images","space biology photos","research team gallery","space technology images","orbital research pictures","space laboratory photos"],canonical:"https://researchsat.space/gallery",structuredData:{"@context":"https://schema.org","@type":"ImageGallery",name:"ResearchSat Gallery - Space Biology Research Images",description:"Explore our comprehensive gallery showcasing space missions, research team, payloads, and scientific activities in microgravity research.",url:"https://researchsat.space/gallery",publisher:{"@type":"Organization",name:"ResearchSat"},image:["https://researchsat.space/src/assets/images/gallery/galleryhr.jpeg"]},breadcrumbs:[{name:"Home",url:"https://researchsat.space"},{name:"Gallery",url:"https://researchsat.space/gallery"}],ogType:"website",ogImage:t}),e.jsxs("div",{className:i.galleryPage,children:[e.jsxs("section",{className:i.heroSection,children:[e.jsx("img",{src:t,alt:"Space background",className:i.heroBackground}),e.jsx("div",{className:i.gradientOverlay}),e.jsx("div",{className:i.contentContainer,children:e.jsxs("div",{className:i.heroContent,children:[e.jsx("div",{className:i.galleryLabel,children:"_Gallery"}),e.jsx("h1",{className:i.heroTitle,children:"Discover our world through images"})]})}),e.jsx("div",{className:i.bottomContainer,children:e.jsxs("div",{className:i.descriptionContainer,children:[e.jsx("p",{className:i.descriptionText,children:"Explore our journey through space research, showcasing past missions, our dedicated team, and groundbreaking activities."}),e.jsx("div",{style:{textAlign:"right"},children:e.jsx("a",{href:"/book-mission",style:{textDecoration:"none"},children:e.jsx("span",{style:{background:"linear-gradient(180deg, #BBDED0 0%, #87C2AA 100%)",WebkitBackgroundClip:"text",backgroundClip:"text",WebkitTextFillColor:"transparent",fontSize:"16px",fontWeight:500,lineHeight:"120%",letterSpacing:"0.25px",fontFamily:"Poppins, sans-serif"},children:"...explore our missions"})})})]})})]}),e.jsx("section",{className:i.gallerySection,children:e.jsxs("div",{className:i.container,children:[e.jsx("h2",{className:i.sectionTitle,children:"Space Missions"}),e.jsx("div",{className:i.bentoGrid,children:$.map(((s,a)=>e.jsxs("div",{className:`${i.bentoItem} ${i[s.position]}`,onClick:()=>J(a,$,X("mission")),children:[e.jsx("img",{src:s.src,alt:s.alt,className:i.bentoImage,loading:"lazy"}),e.jsx("div",{className:i.imageOverlay,children:e.jsx("span",{className:i.imageCategory,children:"Mission"})})]},`mission-${a}`)))})]})}),e.jsx("section",{className:i.gallerySection,children:e.jsxs("div",{className:i.container,children:[e.jsx("h2",{className:i.sectionTitle,children:"Our Team"}),e.jsx("div",{className:i.bentoGrid,children:F.map(((s,a)=>e.jsxs("div",{className:`${i.bentoItem} ${i[s.position]}`,onClick:()=>J(a,F,X("team")),children:[e.jsx("img",{src:s.src,alt:s.alt,className:i.bentoImage,loading:"lazy"}),e.jsx("div",{className:i.imageOverlay,children:e.jsx("span",{className:i.imageCategory,children:"Team"})})]},`team-${a}`)))})]})}),e.jsx("section",{className:i.gallerySection,children:e.jsxs("div",{className:i.container,children:[e.jsx("h2",{className:i.sectionTitle,children:"Payloads & Equipment"}),e.jsx("div",{className:i.bentoGrid,children:q.map(((s,a)=>e.jsxs("div",{className:`${i.bentoItem} ${i[s.position]}`,onClick:()=>J(a,q,X("payload")),children:[e.jsx("img",{src:s.src,alt:s.alt,className:i.bentoImage,loading:"lazy"}),e.jsx("div",{className:i.imageOverlay,children:e.jsx("span",{className:i.imageCategory,children:"Payload"})})]},`payload-${a}`)))})]})}),e.jsx("section",{className:i.gallerySection,children:e.jsxs("div",{className:i.container,children:[e.jsx("h2",{className:i.sectionTitle,children:"Research Activities"}),e.jsx("div",{className:i.bentoGrid,children:z.map(((s,a)=>e.jsxs("div",{className:`${i.bentoItem} ${i[s.position]}`,onClick:()=>J(a,z,X("activity")),children:[e.jsx("img",{src:s.src,alt:s.alt,className:i.bentoImage,loading:"lazy"}),e.jsx("div",{className:i.imageOverlay,children:e.jsx("span",{className:i.imageCategory,children:"Activity"})})]},`activity-${a}`)))})]})}),e.jsx("section",{className:i.ctaSection,children:e.jsx("div",{className:i.container,children:e.jsxs("div",{className:i.ctaContent,children:[e.jsx("h2",{className:i.ctaTitle,children:"Ready to be part of our story?"}),e.jsx("p",{className:i.ctaDescription,children:"Join us in advancing space research and be featured in our next gallery showcase."}),e.jsxs("div",{className:i.ctaButtons,children:[e.jsx("a",{href:"/book-mission",className:i.primaryButton,children:"Book Mission"}),e.jsx("a",{href:"/contact",className:i.secondaryButton,children:"Contact Us"})]})]})})}),e.jsx("div",{className:i.footerMargin}),D&&E.length>0&&e.jsx("div",{className:i.slideshowOverlay,onClick:V,children:e.jsxs("div",{className:i.slideshowContainer,onClick:e=>e.stopPropagation(),children:[e.jsx("button",{className:i.closeButton,onClick:V,children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:i.navButton,onClick:H,style:{left:"20px"},children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15 18L9 12L15 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:i.navButton,onClick:Z,style:{right:"20px"},children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9 18L15 12L9 6",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("div",{className:i.slideshowImageContainer,children:e.jsx("img",{src:null==(R=E[A])?void 0:R.src,alt:null==(M=E[A])?void 0:M.alt,className:i.slideshowImage})}),e.jsxs("div",{className:i.slideshowInfo,children:[e.jsx("h3",{className:i.slideshowTitle,children:null==(P=E[A])?void 0:P.alt}),e.jsxs("p",{className:i.slideshowCounter,children:[A+1," of ",E.length]})]}),e.jsx("div",{className:i.thumbnailContainer,children:E.map(((s,a)=>e.jsx("div",{className:`${i.thumbnail} ${a===A?i.activeThumbnail:""}`,onClick:()=>O(a),children:e.jsx("img",{src:s.src,alt:s.alt})},a)))})]})})]})]})};export{R as default};
