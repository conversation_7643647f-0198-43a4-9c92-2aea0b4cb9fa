<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-M2YTMHZ1JG"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-M2YTMHZ1JG');
    </script>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- SEO Meta Tags -->
    <meta name="keywords" content="ResearchSat, space biology, microbiology,
     life sciences, microgravity research, drug development, satellite platforms,
     space experiments, accelerated drug formulations, unique research opportunities,
     Space Biology, Biology in space, Satellites
    microbiology in space, biology in Satellites, space startup, satellite startup
    biotech startup, space biotech, defense projects, research in space, science
    in space, Astrobiology, biosat, biology payloads, space payloads, satellite
    payloads, experiments in payloads, experiments in satellite payloads,
     payload experiments, Aus space agency, Australian space agency, Australia space agency
     australia space biology, australia microbiology, Ausbiotech space, Ausspace Biotech,
     Aus space biotech, space pharma, aus space pharma, australia space pharma,
     Australia space startups, Australia bio tech startup, Bitech startups in Australia,
     bio satellites of Australia, australia bio satellites, space medtech, medtech australia
     space, space medtech, Australia space medicine,">

    <meta name="google-site-verification" content="ixmBHOv0cIZLlSEkKjaioTyHYEtkAarmtGKU-GU62EI" />
    <meta name="description" content="Discover ResearchSat's cutting-edge space satellites for
    microbiology & life sciences, harnessing microgravity to accelerate drug development &
    unlock breakthrough research.
    ResearchSat -- Satellites for Life-Sciences.
    ResearchSat develops CubeSat payloads capable of Life-Sciences reseach in
    space. ResearchSat develops affordable and sharable satellite's infrastructures
    of researchers and life-sciences companies">
    <meta name="author" content="RaviTeja">

    <!-- OG Meta Tags -->
    <meta property="og:site_name" content="ResearchSat" />
    <meta property="og:site" content="https://researchsat.space" />
    <meta property="og:title" content="ResearchSat -- Satellites for Life-Sciences.
    Space Biology & Microgravity Research Satellites | ResearchSat."/>
    <meta property="og:description" content="Discover ResearchSat's cutting-edge space satellites for
    microbiology & life sciences, harnessing microgravity to accelerate drug development &
    unlock breakthrough research.
    ResearchSat -- Satellites for Life-Sciences." />
    <meta property="og:image" content="" />
    <meta property="og:url" content="" />
    <meta property="og:type" content="article" />

    <!-- Website Title -->
    <title>Space Biology & Microgravity Research Satellites | ResearchSat</title>

    <!-- Styles -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&family=Exo+2:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Favicon  -->
    <link rel="icon" href="/assets/svg/favicon-B0UfWMgK.svg">

    <!-- Main entry point for Vite -->
  <script type="module" crossorigin src="/assets/js/main-CRj8puO7.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/js/vendor-react-BRbkoAi_.js">
  <link rel="modulepreload" crossorigin href="/assets/js/vendor-utils-DOb1KAbh.js">
  <link rel="stylesheet" crossorigin href="/assets/css/main-DOWCUP7_.css">
</head>
<body data-spy="scroll" data-target=".fixed-top" class="index-page">

    <!-- Preloader -->
    <div class="spinner-wrapper">
        <div class="spinner">
          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 30.999999 44.000001" version="1.1" id="svg8">
            <g id="layer1">
              <g id="g922" transform="matrix(0.09925534,0,0,0.09925534,-13.178261,8.3102987)">
                <path id="path894" d="m 313.61289,166.52043 -22.75416,-45.08235 c -9.49325,3.91054 -18.29329,7.14375 -26.73879,11.1125 -4.16455,1.96056 -7.62,2.41829 -10.54894,1.69069 l -5.35517,7.08289 c 18.62667,11.31888 41.10831,16.30098 65.39706,25.19627 z" class="cls-1"></path>
                <path id="path896" d="M 243.55917,124.04423 C 232.33554,99.84808 238.79667,76.419226 261.38679,62.031185 251.26648,41.91756 241.74148,22.420415 231.60794,3.2566439 c -2.0955,-3.96874992 -5.87904,-8.2020832 -9.88484,-9.7895832 -20.73539,-8.2020837 -41.80416,-15.5945417 -65.55316,-24.2940417 4.23333,8.098896 7.23106,13.697479 10.09385,19.364854 19.73527,39.092187 39.20067,78.316666 59.29313,117.231587 6.51668,12.62062 11.85862,28.99833 22.65891,35.56264 l 5.35517,-7.0829 c -4.318,-1.08214 -7.48506,-4.75456 -10.01183,-10.20497 z" class="cls-2"></path>
                <path id="path898" d="M 411.27589,186.84837 C 391.54062,147.75618 372.07523,108.53171 351.98277,69.616789 345.47402,56.99881 340.13208,40.621102 329.33179,34.056789 l -5.35517,7.082896 c 4.318,1.074208 7.48507,4.7625 10.01184,10.197042 11.22362,24.196145 4.7625,47.624999 -17.82763,62.013043 10.12031,20.11362 19.64531,39.61077 29.77886,58.77454 2.0955,3.96875 5.87904,8.20208 9.88483,9.78958 20.73539,8.20208 41.80416,15.59454 65.55316,24.29404 -4.22539,-8.09096 -7.23106,-13.68954 -10.10179,-19.35956 z" class="cls-1"></path>
                <path id="path900" d="m 263.93473,8.8605189 22.75416,45.0823531 c 9.49325,-3.910541 18.2933,-7.143749 26.7388,-11.112499 4.16454,-1.960563 7.62,-2.418292 10.54893,-1.690688 l 5.35517,-7.082896 C 310.70512,22.737915 288.22348,17.75581 263.93473,8.8605189 Z" class="cls-2"></path>
              </g>
            </g>
          </svg>
        </div>
    </div>
    <!-- end of preloader -->

    <!-- Inline preloader script for faster loading -->
    <script>
      // Function to hide preloader
      const hidePreloader = () => {
        const preloaderFadeOutTime = 500;
        const preloader = document.querySelector('.spinner-wrapper');

        if (preloader) {
          // Add transition for smooth fade out
          preloader.style.transition = 'opacity ' + preloaderFadeOutTime + 'ms';
          preloader.style.opacity = '0';

          setTimeout(() => {
            preloader.style.display = 'none';
          }, preloaderFadeOutTime);
        }
      };

      // Hide preloader when DOM is fully loaded
      if (document.readyState === 'complete') {
        hidePreloader();
      } else {
        window.addEventListener('load', hidePreloader);

        // Fallback - hide preloader after 3 seconds even if page isn't fully loaded
        setTimeout(hidePreloader, 3000);
      }
    </script>

    <!-- Main content will be loaded here -->
    <div id="app"></div>
</body>
</html>
